{"version": 3, "file": "form2.mjs", "sources": ["../../../../../../packages/components/form/src/form.vue"], "sourcesContent": ["<template>\n  <form ref=\"formRef\" :class=\"formClasses\">\n    <slot />\n  </form>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, reactive, ref, toRefs, watch } from 'vue'\nimport { debugWarn, isFunction } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormSize } from './hooks'\nimport { formContextKey } from './constants'\nimport { formEmits, formProps } from './form'\nimport { filterFields, useFormLabelWidth } from './utils'\n\nimport type { ValidateFieldsError } from 'async-validator'\nimport type { Arrayable } from '@element-plus/utils'\nimport type {\n  FormContext,\n  FormItemContext,\n  FormValidateCallback,\n  FormValidationResult,\n} from './types'\nimport type { FormItemProp } from './form-item'\n\nconst COMPONENT_NAME = 'ElForm'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(formProps)\nconst emit = defineEmits(formEmits)\n\nconst formRef = ref<HTMLElement>()\nconst fields = reactive<FormItemContext[]>([])\n\nconst formSize = useFormSize()\nconst ns = useNamespace('form')\nconst formClasses = computed(() => {\n  const { labelPosition, inline } = props\n  return [\n    ns.b(),\n    ns.m(formSize.value || 'default'),\n    {\n      [ns.m(`label-${labelPosition}`)]: labelPosition,\n      [ns.m('inline')]: inline,\n    },\n  ]\n})\n\nconst getField: FormContext['getField'] = (prop) => {\n  return filterFields(fields, [prop])[0]\n}\n\nconst addField: FormContext['addField'] = (field) => {\n  fields.push(field)\n}\n\nconst removeField: FormContext['removeField'] = (field) => {\n  if (field.prop) {\n    fields.splice(fields.indexOf(field), 1)\n  }\n}\n\nconst resetFields: FormContext['resetFields'] = (properties = []) => {\n  if (!props.model) {\n    debugWarn(COMPONENT_NAME, 'model is required for resetFields to work.')\n    return\n  }\n  filterFields(fields, properties).forEach((field) => field.resetField())\n}\n\nconst clearValidate: FormContext['clearValidate'] = (props = []) => {\n  filterFields(fields, props).forEach((field) => field.clearValidate())\n}\n\nconst isValidatable = computed(() => {\n  const hasModel = !!props.model\n  if (!hasModel) {\n    debugWarn(COMPONENT_NAME, 'model is required for validate to work.')\n  }\n  return hasModel\n})\n\nconst obtainValidateFields = (props: Arrayable<FormItemProp>) => {\n  if (fields.length === 0) return []\n\n  const filteredFields = filterFields(fields, props)\n  if (!filteredFields.length) {\n    debugWarn(COMPONENT_NAME, 'please pass correct props!')\n    return []\n  }\n  return filteredFields\n}\n\nconst validate = async (\n  callback?: FormValidateCallback\n): FormValidationResult => validateField(undefined, callback)\n\nconst doValidateField = async (\n  props: Arrayable<FormItemProp> = []\n): Promise<boolean> => {\n  if (!isValidatable.value) return false\n\n  const fields = obtainValidateFields(props)\n  if (fields.length === 0) return true\n\n  let validationErrors: ValidateFieldsError = {}\n  for (const field of fields) {\n    try {\n      await field.validate('')\n      if (field.validateState === 'error') field.resetField()\n    } catch (fields) {\n      validationErrors = {\n        ...validationErrors,\n        ...(fields as ValidateFieldsError),\n      }\n    }\n  }\n\n  if (Object.keys(validationErrors).length === 0) return true\n  return Promise.reject(validationErrors)\n}\n\nconst validateField: FormContext['validateField'] = async (\n  modelProps = [],\n  callback\n) => {\n  let result = false\n  const shouldThrow = !isFunction(callback)\n  try {\n    result = await doValidateField(modelProps)\n    // When result is false meaning that the fields are not validatable\n    if (result === true) {\n      await callback?.(result)\n    }\n    return result\n  } catch (e) {\n    if (e instanceof Error) throw e\n\n    const invalidFields = e as ValidateFieldsError\n\n    if (props.scrollToError) {\n      // form-item may be dynamically rendered based on the judgment conditions, and the order in invalidFields is uncertain.\n      // Therefore, the first form field with an error is determined by directly looking for the rendered element.\n      if (formRef.value) {\n        const formItem = formRef.value!.querySelector(\n          `.${ns.b()}-item.is-error`\n        )\n        formItem?.scrollIntoView(props.scrollIntoViewOptions)\n      }\n    }\n    !result && (await callback?.(false, invalidFields))\n    return shouldThrow && Promise.reject(invalidFields)\n  }\n}\n\nconst scrollToField = (prop: FormItemProp) => {\n  const field = getField(prop)\n  if (field) {\n    field.$el?.scrollIntoView(props.scrollIntoViewOptions)\n  }\n}\n\nwatch(\n  () => props.rules,\n  () => {\n    if (props.validateOnRuleChange) {\n      validate().catch((err) => debugWarn(err))\n    }\n  },\n  { deep: true, flush: 'post' }\n)\n\nprovide(\n  formContextKey,\n  reactive({\n    ...toRefs(props),\n    emit,\n\n    resetFields,\n    clearValidate,\n    validateField,\n    getField,\n    addField,\n    removeField,\n\n    ...useFormLabelWidth(),\n  })\n)\n\ndefineExpose({\n  /**\n   * @description Validate the whole form. Receives a callback or returns `Promise`.\n   */\n  validate,\n  /**\n   * @description Validate specified fields.\n   */\n  validateField,\n  /**\n   * @description Reset specified fields and remove validation result.\n   */\n  resetFields,\n  /**\n   * @description Clear validation message for specified fields.\n   */\n  clearValidate,\n  /**\n   * @description Scroll to the specified fields.\n   */\n  scrollToField,\n  /**\n   * @description Get a field context.\n   */\n  getField,\n  /**\n   * @description All fields context.\n   */\n  fields,\n})\n</script>\n"], "names": ["props", "fields", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_renderSlot"], "mappings": ";;;;;;;;;;;mCA0Bc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAIA,IAAA,MAAM,UAAU,GAAiB,EAAA,CAAA;AACjC,IAAM,MAAA,MAAA,GAAS,QAA4B,CAAA,EAAE,CAAA,CAAA;AAE7C,IAAA,MAAM,WAAW,WAAY,EAAA,CAAA;AAC7B,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAM,MAAA,EAAE,aAAe,EAAA,MAAA,EAAW,GAAA,KAAA,CAAA;AAClC,MAAO,OAAA;AAAA,QACL,GAAG,CAAE,EAAA;AAAA,QACL,EAAG,CAAA,CAAA,CAAE,QAAS,CAAA,KAAA,IAAS,SAAS,CAAA;AAAA,QAChC;AAAA,UACE,CAAC,EAAG,CAAA,CAAA,CAAE,SAAS,aAAa,CAAA,CAAE,CAAC,GAAG,aAAA;AAAA,UAClC,CAAC,EAAA,CAAG,CAAE,CAAA,QAAQ,CAAC,GAAG,MAAA;AAAA,SACpB;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAoC,CAAC,IAAS,KAAA;AAClD,MAAA,OAAO,aAAa,MAAQ,EAAA,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA,CAAA;AAAA,KACvC,CAAA;AAEA,IAAM,MAAA,QAAA,GAAoC,CAAC,KAAU,KAAA;AACnD,MAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAM,MAAA,WAAA,GAA0C,CAAC,KAAU,KAAA;AACzD,MAAA,IAAI,MAAM,IAAM,EAAA;AACd,QAAA,MAAA,CAAO,MAAO,CAAA,MAAA,CAAO,OAAQ,CAAA,KAAK,GAAG,CAAC,CAAA,CAAA;AAAA,OACxC;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,WAA0C,GAAA,CAAC,UAAa,GAAA,EAAO,KAAA;AACnE,MAAI,IAAA,CAAC,MAAM,KAAO,EAAA;AAChB,QAAA,SAAA,CAAU,gBAAgB,4CAA4C,CAAA,CAAA;AACtE,QAAA,OAAA;AAAA,OACF;AACA,MAAa,YAAA,CAAA,MAAA,EAAQ,UAAU,CAAE,CAAA,OAAA,CAAQ,CAAC,KAAU,KAAA,KAAA,CAAM,YAAY,CAAA,CAAA;AAAA,KACxE,CAAA;AAEA,IAAA,MAAM,aAA8C,GAAA,CAACA,MAAQ,GAAA,EAAO,KAAA;AAClE,MAAa,YAAA,CAAA,MAAA,EAAQA,MAAK,CAAE,CAAA,OAAA,CAAQ,CAAC,KAAU,KAAA,KAAA,CAAM,eAAe,CAAA,CAAA;AAAA,KACtE,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AACnC,MAAM,MAAA,QAAA,GAAW,CAAC,CAAC,KAAM,CAAA,KAAA,CAAA;AACzB,MAAA,IAAI,CAAC,QAAU,EAAA;AACb,QAAA,SAAA,CAAU,gBAAgB,yCAAyC,CAAA,CAAA;AAAA,OACrE;AACA,MAAO,OAAA,QAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,oBAAA,GAAuB,CAACA,MAAmC,KAAA;AAC/D,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,CAAG;AAEzB,QAAM,OAAA,EAAA,CAAA;AACN,MAAI,oBAAgB,GAAQ,YAAA,CAAA,MAAA,EAAA,MAAA,CAAA,CAAA;AAC1B,MAAA,IAAA,CAAA,uBAA0B;AAC1B,QAAA,SAAQ,CAAA,cAAA,EAAA,4BAAA,CAAA,CAAA;AAAA,QACV,OAAA,EAAA,CAAA;AACA,OAAO;AAAA,MACT,OAAA,cAAA,CAAA;AAEA,KAAA,CAAA;AAIA,IAAA,MAAM,QAAkB,GAAA,OAAA,QACW,KAAA,aACZ,CAAA,KAAA,CAAA,EAAA,QAAA,CAAA,CAAA;AACrB,IAAI,MAAA,eAAe,GAAA,OAAc,MAAA,GAAA,EAAA,KAAA;AAEjC,MAAMC,IAAAA,CAAAA;AACN,QAAIA,OAAAA,KAAkB,CAAA;AAEtB,MAAA,oCAA6C,CAAA,MAAA,CAAA,CAAA;AAC7C,MAAA,IAAA,OAAW,YAAiB,CAAA;AAC1B,QAAI,OAAA,IAAA,CAAA;AACF,MAAM,IAAA,qBAAe,CAAE;AACvB,MAAA,KAAA,MAAU,KAAA,IAAA,OAAA,EAAA;AAA4C,QACxD;AACE,UAAmB,MAAA,KAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA;AAAA,UAAA,IACd,KAAA,CAAA,aAAA,KAAA,OAAA;AAAA,YACH,KAAIA,CAAAA,UAAAA,EAAAA,CAAAA;AAAA,SACN,CAAA,OAAA,OAAA,EAAA;AAAA,UACF,gBAAA,GAAA;AAAA,YACF,GAAA,gBAAA;AAEA,sBAAgB;AAChB,WAAO,CAAA;AAA+B,SACxC;AAEA,OAAA;AAIE,MAAA,IAAI,MAAS,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,MAAA,KAAA,CAAA;AACb,QAAM,OAAA,IAAA,CAAA;AACN,MAAI,OAAA,OAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,CAAA;AACF,KAAS,CAAA;AAET,IAAA,MAAA,aAAe,GAAM,OAAA,UAAA,GAAA,EAAA,EAAA,QAAA,KAAA;AACnB,MAAA,IAAA,MAAM;AAAiB,MACzB,MAAA,WAAA,GAAA,CAAA,UAAA,CAAA,QAAA,CAAA,CAAA;AACA,MAAO,IAAA;AAAA,iBACG,MAAA,eAAA,CAAA,UAAA,CAAA,CAAA;AACV,QAAI,IAAA,MAAA,KAAA;AAEJ,UAAA,OAAsB,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAEtB,SAAA;AAGE,QAAA,cAAY;AACV,OAAM,CAAA,OAAA,CAAA,EAAA;AAA0B,QAC9B,IAAA,CAAA,YAAU,KAAA;AAAA,UACZ,MAAA,CAAA,CAAA;AACA,QAAU,MAAA,aAAA,GAAA,CAAA,CAAA;AAA0C,QACtD,IAAA,KAAA,CAAA,aAAA,EAAA;AAAA,UACF,IAAA,OAAA,CAAA,KAAA,EAAA;AACA,YAAY,MAAA,QAAiB,GAAA,OAAA,CAAA,KAAA,CAAO,aAAa,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,CAAA,cAAA,CAAA,CAAA,CAAA;AACjD,YAAO,QAAA,IAAA,IAAA,GAAuB,KAAA,CAAA,GAAA,QAAoB,CAAA,cAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,CAAA;AAAA,WACpD;AAAA,SACF;AAEA,QAAM,CAAA,MAAA,IAAA,OAAgB,QAAwB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AAC5C,QAAM,OAAA,eAAqB,OAAA,CAAA,MAAA,CAAA,aAAA,CAAA,CAAA;AAC3B,OAAA;AACE,KAAM,CAAA;AAA+C,IACvD,MAAA,aAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACF,IAAA,EAAA,CAAA;AAEA,MAAA,MAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACE,SAAY,EAAA;AAAA,QACN,CAAA,EAAA,GAAA,KAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,CAAA;AACJ,OAAA;AACE,KAAA,CAAA;AAAwC,IAC1C,KAAA,CAAA,MAAA,KAAA,CAAA,KAAA,EAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,oBAAA,EAAA;AAAA,QACE,QAAY,EAAA,CAAA,KAAA,CAAA,CAAA,GAAc,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,OAC9B;AAEA,KAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,MAAA,EAAA,CAAA,CAAA;AAAA,IACE,OAAA,CAAA,cAAA,EAAA,QAAA,CAAA;AAAA,MACA,GAAS,MAAA,CAAA,KAAA,CAAA;AAAA,MACP,IAAA;AAAe,MACf,WAAA;AAAA,MAEA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,GAAA,iBAAA,EAAA;AAAA,KAAA,CAAA,CAAA,CAEA;AAAqB,IAAA,MACtB,CAAA;AAAA,MACH,QAAA;AAEA,MAAa,aAAA;AAAA,MAAA,WAAA;AAAA,MAAA,aAAA;AAAA,MAAA,aAAA;AAAA,MAIX,QAAA;AAAA,MAAA,MAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAIA,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,WAAA,CAAA,CAAA;AAAA,OAIA,EAAA;AAAA,QAAAC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,GAIA;AAAA,CAAA,CAAA,CAAA;AAAA,WAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}