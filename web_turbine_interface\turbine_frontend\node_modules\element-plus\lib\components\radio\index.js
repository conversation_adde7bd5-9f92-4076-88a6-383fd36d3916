'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var radio$1 = require('./src/radio2.js');
var radioButton$1 = require('./src/radio-button.js');
var radioGroup$1 = require('./src/radio-group2.js');
var radio = require('./src/radio.js');
var radioGroup = require('./src/radio-group.js');
var radioButton = require('./src/radio-button2.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElRadio = install.withInstall(radio$1["default"], {
  RadioButton: radioButton$1["default"],
  RadioGroup: radioGroup$1["default"]
});
const ElRadioGroup = install.withNoopInstall(radioGroup$1["default"]);
const ElRadioButton = install.withNoopInstall(radioButton$1["default"]);

exports.radioEmits = radio.radioEmits;
exports.radioProps = radio.radioProps;
exports.radioPropsBase = radio.radioPropsBase;
exports.radioGroupEmits = radioGroup.radioGroupEmits;
exports.radioGroupProps = radioGroup.radioGroupProps;
exports.radioButtonProps = radioButton.radioButtonProps;
exports.radioGroupKey = constants.radioGroupKey;
exports.ElRadio = ElRadio;
exports.ElRadioButton = ElRadioButton;
exports.ElRadioGroup = ElRadioGroup;
exports["default"] = ElRadio;
//# sourceMappingURL=index.js.map
