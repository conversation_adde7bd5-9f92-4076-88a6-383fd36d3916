{"version": 3, "file": "steps2.mjs", "sources": ["../../../../../../packages/components/steps/src/steps.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.m(simple ? 'simple' : direction)]\">\n    <slot />\n    <steps-sorter />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { getCurrentInstance, provide, watch } from 'vue'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { useNamespace, useOrderedChildren } from '@element-plus/hooks'\nimport { stepsEmits, stepsProps } from './steps'\nimport { STEPS_INJECTION_KEY } from './tokens'\n\nimport type { StepItemState } from './item.vue'\n\ndefineOptions({\n  name: 'ElSteps',\n})\n\nconst props = defineProps(stepsProps)\nconst emit = defineEmits(stepsEmits)\n\nconst ns = useNamespace('steps')\nconst {\n  children: steps,\n  addChild: addStep,\n  removeChild: removeStep,\n  ChildrenSorter: StepsSorter,\n} = useOrderedChildren<StepItemState>(getCurrentInstance()!, 'ElStep')\n\nwatch(steps, () => {\n  steps.value.forEach((instance: StepItemState, index: number) => {\n    instance.setIndex(index)\n  })\n})\n\nprovide(STEPS_INJECTION_KEY, { props, steps, addStep, removeStep })\n\nwatch(\n  () => props.active,\n  (newVal: number, oldVal: number) => {\n    emit(CHANGE_EVENT, newVal, oldVal)\n  }\n)\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;mCAgBc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA;AAAA,MACJ,QAAU,EAAA,KAAA;AAAA,MACV,QAAU,EAAA,OAAA;AAAA,MACV,WAAa,EAAA,UAAA;AAAA,MACb,cAAgB,EAAA,WAAA;AAAA,KACd,GAAA,kBAAA,CAAkC,kBAAmB,EAAA,EAAI,QAAQ,CAAA,CAAA;AAErE,IAAA,KAAA,CAAM,OAAO,MAAM;AACjB,MAAA,KAAA,CAAM,KAAM,CAAA,OAAA,CAAQ,CAAC,QAAA,EAAyB,KAAkB,KAAA;AAC9D,QAAA,QAAA,CAAS,SAAS,KAAK,CAAA,CAAA;AAAA,OACxB,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAA,OAAA,CAAQ,qBAAqB,EAAE,KAAA,EAAO,KAAO,EAAA,OAAA,EAAS,YAAY,CAAA,CAAA;AAElE,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,MAAA,EAAA,CAAA,MAAA,EAAA,MAAA,KAAA;AAAA,MACE,iBAAY,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA;AAAA,KACZ,CAAA,CAAC;AACC,IAAK,OAAA,CAAA,IAAA,EAAA,MAAA;AAA4B,MACnC,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,EAAAC,cAAA,CAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,GAAA,QAAA,GAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;"}