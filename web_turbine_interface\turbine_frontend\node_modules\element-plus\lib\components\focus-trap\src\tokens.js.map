{"version": 3, "file": "tokens.js", "sources": ["../../../../../../packages/components/focus-trap/src/tokens.ts"], "sourcesContent": ["import type { InjectionKey, Ref } from 'vue'\n\nexport const FOCUS_AFTER_TRAPPED = 'focus-trap.focus-after-trapped'\nexport const FOCUS_AFTER_RELEASED = 'focus-trap.focus-after-released'\nexport const FOCUSOUT_PREVENTED = 'focus-trap.focusout-prevented'\nexport const FOCUS_AFTER_TRAPPED_OPTS: EventInit = {\n  cancelable: true,\n  bubbles: false,\n}\nexport const FOCUSOUT_PREVENTED_OPTS: EventInit = {\n  cancelable: true,\n  bubbles: false,\n}\n\nexport const ON_TRAP_FOCUS_EVT = 'focusAfterTrapped'\nexport const ON_RELEASE_FOCUS_EVT = 'focusAfterReleased'\n\nexport type FocusTrapInjectionContext = {\n  focusTrapRef: Ref<HTMLElement | undefined>\n  onKeydown: (e: KeyboardEvent) => void\n}\n\nexport const FOCUS_TRAP_INJECTION_KEY: InjectionKey<FocusTrapInjectionContext> =\n  Symbol('elFocusTrap')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,mBAAmB,GAAG,iCAAiC;AACxD,MAAC,oBAAoB,GAAG,kCAAkC;AAC1D,MAAC,kBAAkB,GAAG,gCAAgC;AACtD,MAAC,wBAAwB,GAAG;AACxC,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,OAAO,EAAE,KAAK;AAChB,EAAE;AACU,MAAC,uBAAuB,GAAG;AACvC,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,OAAO,EAAE,KAAK;AAChB,EAAE;AACU,MAAC,iBAAiB,GAAG,oBAAoB;AACzC,MAAC,oBAAoB,GAAG,qBAAqB;AAC7C,MAAC,wBAAwB,GAAG,MAAM,CAAC,aAAa;;;;;;;;;;;"}