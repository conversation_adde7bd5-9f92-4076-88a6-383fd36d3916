@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

$descriptions-item-label-margin-right: () !default;
$descriptions-item-label-margin-right: map.merge(
  (
    'large': 16px,
    'default': 16px,
    'small': 12px,
  ),
  $descriptions-item-label-margin-right
);

$descriptions-item-vertical-label-padding-bottom: () !default;
$descriptions-item-vertical-label-padding-bottom: map.merge(
  (
    'large': 8px,
    'default': 6px,
    'small': 4px,
  ),
  $descriptions-item-vertical-label-padding-bottom
);

@include b(descriptions) {
  @include e(label) {
    &.#{$namespace}-descriptions__cell.is-bordered-label {
      font-weight: bold;
      color: getCssVar('text-color', 'regular');
      background: getCssVar('descriptions-item-bordered-label-background');
    }

    &:not(.is-bordered-label) {
      color: getCssVar('text-color', 'primary');
      margin-right: map.get($descriptions-item-label-margin-right, 'default');
    }

    &.#{$namespace}-descriptions__cell:not(.is-bordered-label).is-vertical-label {
      padding-bottom: map.get(
        $descriptions-item-vertical-label-padding-bottom,
        'default'
      );
    }
  }

  @include e(content) {
    &.#{$namespace}-descriptions__cell.is-bordered-content {
      color: getCssVar('text-color', 'primary');
    }

    &:not(.is-bordered-label) {
      color: getCssVar('text-color', 'regular');
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      @include e(label) {
        &:not(.is-bordered-label) {
          margin-right: map.get($descriptions-item-label-margin-right, $size);
        }

        &.#{$namespace}-descriptions__cell:not(.is-bordered-label).is-vertical-label {
          padding-bottom: map.get(
            $descriptions-item-vertical-label-padding-bottom,
            $size
          );
        }
      }
    }
  }
}
