{"version": 3, "file": "el.min.js", "sources": ["../../../../packages/locale/lang/el.ts"], "sourcesContent": ["export default {\n  name: 'el',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Εντάξει',\n      clear: 'Καθαρισμός',\n    },\n    datepicker: {\n      now: 'Τώρα',\n      today: 'Σήμερα',\n      cancel: 'Ακύρωση',\n      clear: 'Καθαρισμός',\n      confirm: 'Εντάξει',\n      selectDate: 'Επιλέξτε ημέρα',\n      selectTime: 'Επιλέξτε ώρα',\n      startDate: 'Ημερομηνία Έναρξης',\n      startTime: 'Ωρα Έναρξης',\n      endDate: 'Ημερομηνία Λήξης',\n      endTime: 'Ωρα Λήξης',\n      prevYear: 'Προηγούμενο Έτος',\n      nextYear: 'Επόμενο Έτος',\n      prevMonth: 'Προηγούμενος <PERSON>',\n      nextMonth: 'Επό<PERSON>ενος <PERSON>',\n      year: 'Έτος',\n      month1: 'Ιανουάριος',\n      month2: 'Φεβρουάριος',\n      month3: 'Μάρτιος',\n      month4: 'Απρίλιος',\n      month5: 'Μάιος',\n      month6: 'Ιούνιος',\n      month7: 'Ιούλιος',\n      month8: 'Αύγουστος',\n      month9: 'Σεπτέμβριος',\n      month10: 'Οκτώβριος',\n      month11: 'Νοέμβριος',\n      month12: 'Δεκέμβριος',\n      // week: 'εβδομάδα',\n      weeks: {\n        sun: 'Κυρ',\n        mon: 'Δευ',\n        tue: 'Τρι',\n        wed: 'Τετ',\n        thu: 'Πεμ',\n        fri: 'Παρ',\n        sat: 'Σαβ',\n      },\n      months: {\n        jan: 'Ιαν',\n        feb: 'Φεβ',\n        mar: 'Μαρ',\n        apr: 'Απρ',\n        may: 'Μαϊ',\n        jun: 'Ιουν',\n        jul: 'Ιουλ',\n        aug: 'Αυγ',\n        sep: 'Σεπ',\n        oct: 'Οκτ',\n        nov: 'Νοε',\n        dec: 'Δεκ',\n      },\n    },\n    select: {\n      loading: 'Φόρτωση',\n      noMatch: 'Δεν βρέθηκαν αποτελέσματα',\n      noData: 'Χωρίς δεδομένα',\n      placeholder: 'Επιλογή',\n    },\n    mention: {\n      loading: 'Φόρτωση',\n    },\n    cascader: {\n      noMatch: 'Δεν βρέθηκαν αποτελέσματα',\n      loading: 'Φόρτωση',\n      placeholder: 'Επιλογή',\n      noData: 'Χωρίς δεδομένα',\n    },\n    pagination: {\n      goto: 'Μετάβαση σε',\n      pagesize: '/σελίδα',\n      total: 'Σύνολο {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Μήνυμα',\n      confirm: 'Εντάξει',\n      cancel: 'Ακύρωση',\n      error: 'Άκυρη εισαγωγή',\n    },\n    upload: {\n      deleteTip: 'Πάτησε Διαγραφή για αφαίρεση',\n      delete: 'Διαγραφή',\n      preview: 'Προεπισκόπηση',\n      continue: 'Συνέχεια',\n    },\n    table: {\n      emptyText: 'Χωρίς Δεδομένα',\n      confirmFilter: 'Επιβεβαίωση',\n      resetFilter: 'Επαναφορά',\n      clearFilter: 'Όλα',\n      sumText: 'Σύνολο',\n    },\n    tree: {\n      emptyText: 'Χωρίς Δεδομένα',\n    },\n    transfer: {\n      noMatch: 'Δεν βρέθηκαν αποτελέσματα',\n      noData: 'Χωρίς δεδομένα',\n      titles: ['Λίστα 1', 'Λίστα 2'],\n      filterPlaceholder: 'Αναζήτηση',\n      noCheckedFormat: '{total} Αντικείμενα',\n      hasCheckedFormat: '{checked}/{total} επιλεγμένα',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,KAAK,CAAC,8DAA8D,CAAC,OAAO,CAAC,4CAA4C,CAAC,UAAU,CAAC,iFAAiF,CAAC,UAAU,CAAC,qEAAqE,CAAC,SAAS,CAAC,yGAAyG,CAAC,SAAS,CAAC,+DAA+D,CAAC,OAAO,CAAC,6FAA6F,CAAC,OAAO,CAAC,mDAAmD,CAAC,QAAQ,CAAC,6FAA6F,CAAC,QAAQ,CAAC,qEAAqE,CAAC,SAAS,CAAC,yGAAyG,CAAC,SAAS,CAAC,iFAAiF,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,8DAA8D,CAAC,MAAM,CAAC,oEAAoE,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,wDAAwD,CAAC,MAAM,CAAC,oEAAoE,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,8DAA8D,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,8IAA8I,CAAC,MAAM,CAAC,iFAAiF,CAAC,WAAW,CAAC,4CAA4C,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,8IAA8I,CAAC,OAAO,CAAC,4CAA4C,CAAC,WAAW,CAAC,4CAA4C,CAAC,MAAM,CAAC,iFAAiF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,+DAA+D,CAAC,QAAQ,CAAC,uCAAuC,CAAC,KAAK,CAAC,8CAA8C,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,4CAA4C,CAAC,MAAM,CAAC,4CAA4C,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,2JAA2J,CAAC,MAAM,CAAC,kDAAkD,CAAC,OAAO,CAAC,gFAAgF,CAAC,QAAQ,CAAC,kDAAkD,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iFAAiF,CAAC,aAAa,CAAC,oEAAoE,CAAC,WAAW,CAAC,wDAAwD,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iFAAiF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,8IAA8I,CAAC,MAAM,CAAC,iFAAiF,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,wDAAwD,CAAC,eAAe,CAAC,4EAA4E,CAAC,gBAAgB,CAAC,gFAAgF,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}