{"version": 3, "file": "description-item.mjs", "sources": ["../../../../../../packages/components/descriptions/src/description-item.ts"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { columnAlignment } from '@element-plus/constants'\nimport { buildProps } from '@element-plus/utils'\nimport { COMPONENT_NAME } from './constants'\n\nimport type {\n  ExtractPropTypes,\n  Slot,\n  VNode,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport const descriptionItemProps = buildProps({\n  /**\n   * @description label text\n   */\n  label: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description colspan of column\n   */\n  span: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description the number of rows a cell should span\n   */\n  rowspan: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description column width, the width of the same column in different rows is set by the max value (If no `border`, width contains label and content)\n   */\n  width: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column minimum width, columns with `width` has a fixed width, while columns with `min-width` has a width that is distributed in proportion (If no`border`, width contains label and content)\n   */\n  minWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column label width, if not set, it will be the same as the width of the column. Higher priority than the `label-width` of `Descriptions`\n   */\n  labelWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description column content alignment (If no `border`, effective for both label and content)\n   */\n  align: {\n    type: String,\n    values: columnAlignment,\n    default: 'left',\n  },\n  /**\n   * @description column label alignment, if omitted, the value of the above `align` attribute will be applied (If no `border`, please use `align` attribute)\n   */\n  labelAlign: {\n    type: String,\n    values: columnAlignment,\n  },\n  /**\n   * @description column content custom class name\n   */\n  className: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description column label custom class name\n   */\n  labelClassName: {\n    type: String,\n    default: '',\n  },\n})\n\nconst DescriptionItem = defineComponent({\n  name: COMPONENT_NAME,\n  props: descriptionItemProps,\n})\n\nexport default DescriptionItem\n\nexport type DescriptionItemProps = ExtractPropTypes<typeof descriptionItemProps>\nexport type DescriptionItemPropsPublic = __ExtractPublicPropTypes<\n  typeof descriptionItemProps\n>\nexport type DescriptionItemVNode = VNode & {\n  children: { [name: string]: Slot } | null\n  props: Partial<DescriptionItemProps> | null\n}\n"], "names": [], "mappings": ";;;;;AAIY,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,eAAe;AAC3B,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,eAAe;AAC3B,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC,EAAE;AACE,MAAC,eAAe,GAAG,eAAe,CAAC;AACxC,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,KAAK,EAAE,oBAAoB;AAC7B,CAAC;;;;"}