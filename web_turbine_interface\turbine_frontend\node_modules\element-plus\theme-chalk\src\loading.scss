@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

:root {
  @include set-component-css-var('loading', $loading);
}

@include b(loading-parent) {
  @include m(relative) {
    position: relative !important;
  }

  @include m(hidden) {
    overflow: hidden !important;
  }
}

@include b(loading-mask) {
  position: absolute;
  z-index: 2000;
  background-color: getCssVar('mask-color');
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: opacity getCssVar('transition-duration');

  @include when(fullscreen) {
    position: fixed;

    .#{$namespace}-loading-spinner {
      margin-top: calc(
        (0px - getCssVar('loading-fullscreen-spinner-size')) / 2
      );

      .circular {
        height: getCssVar('loading-fullscreen-spinner-size');
        width: getCssVar('loading-fullscreen-spinner-size');
      }
    }
  }
}

@include b(loading-spinner) {
  top: 50%;
  margin-top: calc((0px - getCssVar('loading-spinner-size')) / 2);
  width: 100%;
  text-align: center;
  position: absolute;

  .#{$namespace}-loading-text {
    color: getCssVar('color-primary');
    margin: 3px 0;
    font-size: 14px;
  }

  .circular {
    display: inline;
    height: getCssVar('loading-spinner-size');
    width: getCssVar('loading-spinner-size');
    animation: loading-rotate 2s linear infinite;
  }

  .path {
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: getCssVar('color-primary');
    stroke-linecap: round;
  }

  i {
    color: getCssVar('color-primary');
  }
}

.#{$namespace}-loading-fade-enter-from,
.#{$namespace}-loading-fade-leave-to {
  opacity: 0;
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
