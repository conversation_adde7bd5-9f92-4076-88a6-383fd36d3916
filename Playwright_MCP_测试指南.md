# 🎭 Playwright MCP 测试指南

## 📋 **测试要求总览**

### **🔴 强制要求**
- **涉及页面UI的开发必须用Playwright MCP进行测试**
- **每个功能完成后立即测试，发现问题立即修复**
- **测试覆盖：UI界面、用户交互、响应式设计、功能完整性**
- **测试环境：真实浏览器环境，多设备尺寸，实际用户操作流程**

### **⭐ 核心价值**
- **真实性验证**: 在真实浏览器中验证功能，而非模拟
- **用户体验保证**: 确保实际用户操作流程的流畅性
- **跨设备兼容**: 验证响应式设计在不同设备上的表现
- **问题早发现**: 开发过程中及时发现和修复UI问题

## 🛠️ **Playwright MCP 工具清单**

### **基础导航工具**
```javascript
// 访问页面
browser_navigate_Playwright(url)

// 页面状态快照
browser_snapshot_Playwright()

// 截图记录
browser_take_screenshot_Playwright(filename)

// 关闭浏览器
browser_close_Playwright()
```

### **交互测试工具**
```javascript
// 点击操作
browser_click_Playwright(element, ref)

// 文本输入
browser_type_Playwright(element, ref, text)

// 悬停操作
browser_hover_Playwright(element, ref)

// 拖拽操作
browser_drag_Playwright(startElement, startRef, endElement, endRef)
```

### **响应式测试工具**
```javascript
// 调整窗口大小
browser_resize_Playwright(width, height)

// 等待元素或文本
browser_wait_for_Playwright(text/textGone/time)

// 选择下拉选项
browser_select_option_Playwright(element, ref, values)
```

### **高级功能工具**
```javascript
// 执行JavaScript
browser_evaluate_Playwright(function, element, ref)

// 文件上传
browser_file_upload_Playwright(paths)

// 处理对话框
browser_handle_dialog_Playwright(accept, promptText)
```

## 📋 **标准测试流程**

### **Step 1: 环境准备**
```bash
1. 确保服务运行
   - 后端: http://localhost:8000
   - 前端: http://localhost:3000
   
2. 检查功能状态
   - WebSocket连接正常
   - 基础功能可用
```

### **Step 2: 基础功能测试**
```javascript
// 1. 访问页面
browser_navigate_Playwright("http://localhost:3000")

// 2. 检查页面加载
browser_snapshot_Playwright()

// 3. 截图记录
browser_take_screenshot_Playwright("initial_load.png")
```

### **Step 3: 交互功能测试**
```javascript
// 4. 测试用户输入
browser_type_Playwright("输入框", "ref123", "测试消息")

// 5. 测试按钮点击
browser_click_Playwright("发送按钮", "ref456")

// 6. 等待响应
browser_wait_for_Playwright("AI正在思考")

// 7. 验证结果
browser_snapshot_Playwright()
```

### **Step 4: 响应式设计测试**
```javascript
// 8. 桌面端测试
browser_resize_Playwright(1200, 800)
browser_take_screenshot_Playwright("desktop_view.png")

// 9. 平板端测试
browser_resize_Playwright(768, 1024)
browser_take_screenshot_Playwright("tablet_view.png")

// 10. 手机端测试
browser_resize_Playwright(375, 667)
browser_take_screenshot_Playwright("mobile_view.png")
```

### **Step 5: 高级功能测试**
```javascript
// 11. 对话框测试
browser_click_Playwright("搜索按钮", "ref789")
browser_snapshot_Playwright()

// 12. 表单填写测试
browser_type_Playwright("搜索框", "ref101", "关键词")

// 13. 功能验证
browser_click_Playwright("搜索", "ref102")
browser_wait_for_Playwright("搜索结果")
```

## 🎯 **测试场景模板**

### **UI界面测试**
```javascript
// 界面元素检查
- 页面标题显示正确
- 导航菜单完整显示
- 主要功能区域布局正常
- 颜色和样式符合设计

// 测试代码示例
browser_navigate_Playwright("http://localhost:3000")
browser_snapshot_Playwright()
// 验证: 检查页面元素是否正确显示
```

### **用户交互测试**
```javascript
// 交互流程测试
- 输入框可以正常输入
- 按钮点击有正确响应
- 表单提交功能正常
- 错误处理显示正确

// 测试代码示例
browser_type_Playwright("消息输入框", "ref", "测试消息")
browser_click_Playwright("发送按钮", "ref")
browser_wait_for_Playwright("消息发送成功")
```

### **响应式设计测试**
```javascript
// 多设备适配测试
- 桌面端: 1200x800, 1920x1080
- 平板端: 768x1024, 1024x768
- 手机端: 375x667, 414x896

// 测试代码示例
const sizes = [
  {width: 1200, height: 800, name: "desktop"},
  {width: 768, height: 1024, name: "tablet"},
  {width: 375, height: 667, name: "mobile"}
]

sizes.forEach(size => {
  browser_resize_Playwright(size.width, size.height)
  browser_take_screenshot_Playwright(`${size.name}_view.png`)
})
```

## 🔍 **问题诊断指南**

### **常见问题类型**
1. **元素定位失败**: 检查ref引用是否正确
2. **点击无响应**: 确认元素是否可见和可点击
3. **输入失败**: 检查输入框是否处于激活状态
4. **等待超时**: 调整等待时间或检查条件

### **调试技巧**
```javascript
// 1. 使用快照调试
browser_snapshot_Playwright()  // 查看当前页面状态

// 2. 使用截图调试
browser_take_screenshot_Playwright("debug.png")  // 保存当前界面

// 3. 使用JavaScript调试
browser_evaluate_Playwright("() => console.log('调试信息')")
```

## 📊 **测试报告模板**

### **测试结果记录**
```markdown
## 测试报告

### 测试环境
- 浏览器: Chrome/Firefox/Safari
- 分辨率: 1200x800 / 768x1024 / 375x667
- 测试时间: 2025-07-26

### 测试结果
- ✅ 页面加载: 正常
- ✅ 用户交互: 流畅
- ✅ 响应式设计: 完美适配
- ❌ 特定功能: 需要修复

### 问题记录
1. 问题描述: 搜索对话框在移动端显示异常
2. 修复方案: 调整CSS响应式样式
3. 验证结果: 修复成功
```

## 🎉 **最佳实践**

### **开发流程集成**
1. **功能开发** → **Playwright MCP测试** → **问题修复** → **再次测试**
2. **每个UI组件完成后立即测试**
3. **响应式设计必须在多个尺寸下验证**
4. **用户交互流程必须完整测试**

### **测试覆盖原则**
- **功能完整性**: 所有功能都能正常工作
- **用户体验**: 交互流畅，反馈及时
- **视觉效果**: 界面美观，布局合理
- **兼容性**: 多设备、多浏览器支持

---

**🎭 记住：Playwright MCP测试是确保Web应用质量的关键工具，每次涉及UI的开发都必须使用！**
