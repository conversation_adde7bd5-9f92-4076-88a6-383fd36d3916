{"version": 3, "file": "form-item.js", "sources": ["../../../../../../packages/components/form/src/form-item.ts"], "sourcesContent": ["import { componentSizes } from '@element-plus/constants'\nimport { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { FormItemRule } from './types'\n\nexport const formItemValidateStates = [\n  '',\n  'error',\n  'validating',\n  'success',\n] as const\nexport type FormItemValidateState = typeof formItemValidateStates[number]\n\nexport type FormItemProp = Arrayable<string>\n\nexport const formItemProps = buildProps({\n  /**\n   * @description Label text.\n   */\n  label: String,\n  /**\n   * @description Width of label, e.g. `'50px'`. `'auto'` is supported.\n   */\n  labelWidth: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description Position of label. If set to `'left'` or `'right'`, `label-width` prop is also required. The default is extend from `form label-position`.\n   */\n  labelPosition: {\n    type: String,\n    values: ['left', 'right', 'top', ''],\n    default: '',\n  },\n  /**\n   * @description  A key of `model`. It could be an array of property paths (e.g `['a', 'b', '0']`). In the use of `validate` and `resetFields` method, the attribute is required.\n   */\n  prop: {\n    type: definePropType<FormItemProp>([String, Array]),\n  },\n  /**\n   * @description Whether the field is required or not, will be determined by validation rules if omitted.\n   */\n  required: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description Validation rules of form, see the [following table](#formitemrule), more advanced usage at [async-validator](https://github.com/yiminghe/async-validator).\n   */\n  rules: {\n    type: definePropType<Arrayable<FormItemRule>>([Object, Array]),\n  },\n  /**\n   * @description Field error message, set its value and the field will validate error and show this message immediately.\n   */\n  error: String,\n  /**\n   * @description Validation state of formItem.\n   */\n  validateStatus: {\n    type: String,\n    values: formItemValidateStates,\n  },\n  /**\n   * @description Same as for in native label.\n   */\n  for: String,\n  /**\n   * @description Inline style validate message.\n   */\n  inlineMessage: {\n    type: [String, Boolean],\n    default: '',\n  },\n  /**\n   * @description Whether to show the error message.\n   */\n  showMessage: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Control the size of components in this form-item.\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n} as const)\nexport type FormItemProps = ExtractPropTypes<typeof formItemProps>\nexport type FormItemPropsPublic = __ExtractPublicPropTypes<typeof formItemProps>\n"], "names": ["buildProps", "definePropType", "componentSizes"], "mappings": ";;;;;;;AAEY,MAAC,sBAAsB,GAAG;AACtC,EAAE,EAAE;AACJ,EAAE,OAAO;AACT,EAAE,YAAY;AACd,EAAE,SAAS;AACX,EAAE;AACU,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC;AACxC,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,sBAAsB;AAClC,GAAG;AACH,EAAE,GAAG,EAAE,MAAM;AACb,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;AAC3B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAEC,mBAAc;AAC1B,GAAG;AACH,CAAC;;;;;"}