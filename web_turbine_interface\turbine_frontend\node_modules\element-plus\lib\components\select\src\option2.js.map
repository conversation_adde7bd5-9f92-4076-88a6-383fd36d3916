{"version": 3, "file": "option2.js", "sources": ["../../../../../../packages/components/select/src/option.vue"], "sourcesContent": ["<template>\n  <li\n    v-show=\"visible\"\n    :id=\"id\"\n    :class=\"containerKls\"\n    role=\"option\"\n    :aria-disabled=\"isDisabled || undefined\"\n    :aria-selected=\"itemSelected\"\n    @mousemove=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot>\n      <span>{{ currentLabel }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  onBeforeUnmount,\n  reactive,\n  toRefs,\n  unref,\n} from 'vue'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport { COMPONENT_NAME, optionProps } from './option'\n\nimport type { OptionExposed, OptionInternalInstance, OptionStates } from './type'\n\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n\n  props: optionProps,\n\n  setup(props) {\n    const ns = useNamespace('select')\n    const id = useId()\n\n    const containerKls = computed(() => [\n      ns.be('dropdown', 'item'),\n      ns.is('disabled', unref(isDisabled)),\n      ns.is('selected', unref(itemSelected)),\n      ns.is('hovering', unref(hover)),\n    ])\n\n    const states = reactive<OptionStates>({\n      index: -1,\n      groupDisabled: false,\n      visible: true,\n      hover: false,\n    })\n\n    const {\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      hoverItem,\n      updateOption,\n    } = useOption(props, states)\n\n    const { visible, hover } = toRefs(states)\n\n    const vm = (getCurrentInstance()! as OptionInternalInstance).proxy\n\n    select.onOptionCreate(vm)\n\n    onBeforeUnmount(() => {\n      const key = vm.value\n      const { selected: selectedOptions } = select.states\n      const doesSelected = selectedOptions.some((item) => {\n        return item.value === vm.value\n      })\n      // if option is not selected, remove it from cache\n      nextTick(() => {\n        if (select.states.cachedOptions.get(key) === vm && !doesSelected) {\n          select.states.cachedOptions.delete(key)\n        }\n      })\n      select.onOptionDestroy(key, vm)\n    })\n\n    function selectOptionClick() {\n      if (!isDisabled.value) {\n        select.handleOptionSelect(vm)\n      }\n    }\n\n    return {\n      ns,\n      id,\n      containerKls,\n      currentLabel,\n      itemSelected,\n      isDisabled,\n      select,\n      visible,\n      hover,\n      states,\n\n      hoverItem,\n      updateOption,\n      selectOptionClick,\n    } satisfies OptionExposed\n  },\n})\n</script>\n"], "names": ["defineComponent", "COMPONENT_NAME", "optionProps", "useNamespace", "useId", "computed", "unref", "reactive", "useOption", "toRefs", "getCurrentInstance", "onBeforeUnmount", "nextTick", "_createElementBlock", "_normalizeClass", "_withModifiers", "_renderSlot", "_createElementVNode", "_toDisplayString", "_vShow", "_export_sfc"], "mappings": ";;;;;;;;;;;AAkCA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAAC,qBAAA;AAAA,EACN,aAAe,EAAAA,qBAAA;AAAA,EAEf,KAAO,EAAAC,kBAAA;AAAA,EAEP,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAKC,mBAAa,QAAQ,CAAA,CAAA;AAChC,IAAA,MAAM,KAAKC,aAAM,EAAA,CAAA;AAEjB,IAAM,MAAA,YAAA,GAAeC,aAAS,MAAM;AAAA,MAClC,EAAA,CAAG,EAAG,CAAA,UAAA,EAAY,MAAM,CAAA;AAAA,MACxB,EAAG,CAAA,EAAA,CAAG,UAAY,EAAAC,SAAA,CAAM,UAAU,CAAC,CAAA;AAAA,MACnC,EAAG,CAAA,EAAA,CAAG,UAAY,EAAAA,SAAA,CAAM,YAAY,CAAC,CAAA;AAAA,MACrC,EAAG,CAAA,EAAA,CAAG,UAAY,EAAAA,SAAA,CAAM,KAAK,CAAC,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAAA,MAAM,SAASC,YAAuB,CAAA;AAAA,MACpC,KAAO,EAAA,CAAA,CAAA;AAAA,MACP,aAAe,EAAA,KAAA;AAAA,MACf,OAAS,EAAA,IAAA;AAAA,MACT,KAAO,EAAA,KAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,YAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,SAAA;AAAA,MACA,YAAA;AAAA,KACF,GAAIC,mBAAU,CAAA,KAAA,EAAO,MAAM,CAAA,CAAA;AAE3B,IAAA,MAAM,EAAE,OAAA,EAAS,KAAM,EAAA,GAAIC,WAAO,MAAM,CAAA,CAAA;AAExC,IAAM,MAAA,EAAA,GAAMC,wBAAiD,CAAA,KAAA,CAAA;AAE7D,IAAA,MAAA,CAAO,eAAe,EAAE,CAAA,CAAA;AAExB,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAA,MAAM,MAAM,EAAG,CAAA,KAAA,CAAA;AACf,MAAA,MAAM,EAAE,QAAA,EAAU,eAAgB,EAAA,GAAI,MAAO,CAAA,MAAA,CAAA;AAC7C,MAAA,MAAM,YAAe,GAAA,eAAA,CAAgB,IAAK,CAAA,CAAC,IAAS,KAAA;AAClD,QAAO,OAAA,IAAA,CAAK,UAAU,EAAG,CAAA,KAAA,CAAA;AAAA,OAC1B,CAAA,CAAA;AAED,MAAAC,YAAA,CAAS,MAAM;AACb,QAAI,IAAA,MAAA,CAAO,OAAO,aAAc,CAAA,GAAA,CAAI,GAAG,CAAM,KAAA,EAAA,IAAM,CAAC,YAAc,EAAA;AAChE,UAAO,MAAA,CAAA,MAAA,CAAO,aAAc,CAAA,MAAA,CAAO,GAAG,CAAA,CAAA;AAAA,SACxC;AAAA,OACD,CAAA,CAAA;AACD,MAAO,MAAA,CAAA,eAAA,CAAgB,KAAK,EAAE,CAAA,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAAA,SAAS,iBAAoB,GAAA;AAC3B,MAAI,IAAA,CAAC,WAAW,KAAO,EAAA;AACrB,QAAA,MAAA,CAAO,mBAAmB,EAAE,CAAA,CAAA;AAAA,OAC9B;AAAA,KACF;AAEA,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MACA,EAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,MAAA;AAAA,MAEA,SAAA;AAAA,MACA,YAAA;AAAA,MACA,iBAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;8CA9GCC,sBAaK,CAAA,IAAA,EAAA;AAAA,IAXF,EAAI,EAAA,IAAA,CAAA,EAAA;AAAA,IACJ,KAAA,EAAKC,mBAAE,IAAY,CAAA,YAAA,CAAA;AAAA,IACpB,IAAK,EAAA,QAAA;AAAA,IACJ,iBAAe,IAAc,CAAA,UAAA,IAAA,KAAA,CAAA;AAAA,IAC7B,eAAe,EAAA,IAAA,CAAA,YAAA;AAAA,IACf,WAAW,EAAA,IAAA,CAAA,SAAA;AAAA,IACX,OAAA,EAAKC,kBAAO,IAAiB,CAAA,iBAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,GAAA,EAAA;AAE9B,IAAAC,cAAA,CAEO,4BAFP,MAEO;AAAA,MADLC,sBAAA,CAAA,MAAA,EAAA,IAAA,EAAAC,mBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAA+B,CAAA;AAAA,GAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,eAAA,EAAA,eAAA,EAAA,aAAA,EAAA,SAAA,CAAA,CAAA,GAAA;AAAA,IAAA,CAAAC,SAAA,EAAA,IAAA,CAAA,OAAtB,CAAY;AAAA,GAAA,CAAA,CAAA;AAAA,CAAA;AAAA,aAAA,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,YAAA,CAAA,CAAA,CAAA;;;;"}