{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/switch/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Switch from './src/switch.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSwitch: SFCWithInstall<typeof Switch> = withInstall(Switch)\nexport default ElSwitch\n\nexport * from './src/switch'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}