'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');

const tabPaneProps = runtime.buildProps({
  label: {
    type: String,
    default: ""
  },
  name: {
    type: [String, Number]
  },
  closable: <PERSON><PERSON>an,
  disabled: <PERSON><PERSON><PERSON>,
  lazy: <PERSON><PERSON><PERSON>
});

exports.tabPaneProps = tabPaneProps;
//# sourceMappingURL=tab-pane.js.map
