/**
 * Determine if the testing element is visible on screen no matter if its on the viewport or not
 */
export declare const isVisible: (element: HTMLElement) => boolean;
export declare const obtainAllFocusableElements: (element: HTMLElement) => HTMLElement[];
/**
 * @desc Determine if target element is focusable
 * @param element {HTMLElement}
 * @returns {Boolean} true if it is focusable
 */
export declare const isFocusable: (element: HTMLElement) => boolean;
/**
 * @desc Set Attempt to set focus on the current node.
 * @param element
 *          The node to attempt to focus on.
 * @returns
 *  true if element is focused.
 */
export declare const attemptFocus: (element: HTMLElement) => boolean;
/**
 * Trigger an event
 * mouseenter, mouseleave, mouseover, keyup, change, click, etc.
 * @param  {HTMLElement} elm
 * @param  {String} name
 * @param  {*} opts
 */
export declare const triggerEvent: (elm: HTMLElement, name: string, ...opts: Array<boolean>) => HTMLElement;
export declare const isLeaf: (el: HTMLElement) => boolean;
export declare const getSibling: (el: HTMLElement, distance: number, elClass: string) => Element | null;
export declare const focusNode: (el: HTMLElement) => void;
