{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/dialog/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Dialog from './src/dialog.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDialog: SFCWithInstall<typeof Dialog> = withInstall(Dialog)\nexport default ElDialog\n\nexport * from './src/use-dialog'\nexport * from './src/dialog'\nexport * from './src/constants'\n"], "names": ["withInstall", "Dialog"], "mappings": ";;;;;;;;;;AAEY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,mBAAM;;;;;;;;;"}