{"version": 3, "file": "mask2.mjs", "sources": ["../../../../../../packages/components/tour/src/mask.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"visible\"\n    :class=\"ns.e('mask')\"\n    :style=\"({\n    position: 'fixed',\n    left: 0,\n    right: 0,\n    top: 0,\n    bottom: 0,\n    zIndex,\n    pointerEvents: pos && targetAreaClickable ? 'none' : 'auto',\n  } as any)\"\n    v-bind=\"$attrs\"\n  >\n    <svg\n      :style=\"{\n        width: '100%',\n        height: '100%',\n      }\"\n    >\n      <path :class=\"ns.e('hollow')\" :style=\"pathStyle\" :d=\"path\" />\n    </svg>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, toRef } from 'vue'\nimport { useLockscreen } from '@element-plus/hooks'\nimport { maskProps } from './mask'\nimport { tourKey } from './helper'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTourMask',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(maskProps)\n\nconst { ns } = inject(tourKey)!\nconst radius = computed(() => props.pos?.radius ?? 2)\nconst roundInfo = computed(() => {\n  const v = radius.value\n  const baseInfo = `a${v},${v} 0 0 1`\n  return {\n    topRight: `${baseInfo} ${v},${v}`,\n    bottomRight: `${baseInfo} ${-v},${v}`,\n    bottomLeft: `${baseInfo} ${-v},${-v}`,\n    topLeft: `${baseInfo} ${v},${-v}`,\n  }\n})\n\nconst path = computed(() => {\n  const width = window.innerWidth\n  const height = window.innerHeight\n  const info = roundInfo.value\n  const _path = `M${width},0 L0,0 L0,${height} L${width},${height} L${width},0 Z`\n  const _radius = radius.value\n  return props.pos\n    ? `${_path} M${props.pos.left + _radius},${props.pos.top} h${\n        props.pos.width - _radius * 2\n      } ${info.topRight} v${props.pos.height - _radius * 2} ${\n        info.bottomRight\n      } h${-props.pos.width + _radius * 2} ${info.bottomLeft} v${\n        -props.pos.height + _radius * 2\n      } ${info.topLeft} z`\n    : _path\n})\n\nconst pathStyle = computed<CSSProperties>(() => {\n  return {\n    fill: props.fill,\n    pointerEvents: 'auto',\n    cursor: 'auto',\n  }\n})\n\nuseLockscreen(toRef(props, 'visible'), {\n  ns,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;mCAkCc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,EAAE,EAAA,EAAO,GAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AAC7B,IAAA,MAAM,SAAS,QAAS,CAAA,MAAM;AAC9B,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,UAAU,GAAO,CAAA,EAAA,GAAA,KAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA;AACjB,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,SAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,gBACQ,CAAA,KAAA,CAAQ;AAAU,MAAA,iBACf,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAQ,UAAU;AAAC,MACnC,OAAA;AAAmC,QACnC,UAAY,CAAA,EAAA,YAAa,CAAA,CAAA,CAAA,EAAK,CAAC,CAAA,CAAA;AAAA,QACjC,WAAA,EAAA,CAAA,EAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,QACD,UAAA,EAAA,CAAA,EAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAED,QAAM,OAAA,EAAO,WAAe,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAC1B,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,eAAuB,CAAA,MAAA;AACvB,MAAM,MAAA,KAAA,GAAQ,MAAI,CAAA,UAAmB,CAAA;AACrC,MAAA,MAAM,eAAiB,CAAA,WAAA,CAAA;AACvB,MAAO,MAAA,IAAA,GAAM,SACN,CAAA;AAOH,MACL,MAAA,KAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA,WAAA,EAAA,MAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAED,MAAM,MAAA,OAAA,GAAY,YAA8B,CAAA;AAC9C,MAAO,OAAA,KAAA,CAAA,GAAA,GAAA,CAAA,EAAA,KAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,CAAA,IAAA,GAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,CAAA,KAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA,EAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,EAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,OAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACO,IAAA,MACG,SAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAAA,OACP;AAAA,QACV,IAAA,EAAA,KAAA,CAAA,IAAA;AAAA,QACD,aAAA,EAAA,MAAA;AAED,QAAc,MAAA,EAAA,MAAA;AAAyB,OACrC,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}