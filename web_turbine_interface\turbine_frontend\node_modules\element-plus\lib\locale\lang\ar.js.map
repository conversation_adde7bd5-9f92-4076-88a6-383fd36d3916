{"version": 3, "file": "ar.js", "sources": ["../../../../../packages/locale/lang/ar.ts"], "sourcesContent": ["export default {\n  name: 'ar',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'موافق',\n      clear: 'إزالة',\n      defaultLabel: 'إختر اللون',\n      description: 'اللون الحالي هو {color}. اضفط انتر لاختيار لون جديد',\n    },\n    datepicker: {\n      now: 'الآن',\n      today: 'اليوم',\n      cancel: 'إلغاء',\n      clear: 'إزالة',\n      confirm: 'موافق',\n      dateTablePrompt:\n        'استخدم مفاتيح الاسهم و اضغط انتر لاختيار اليوم المراد من الشهر',\n      monthTablePrompt: 'استخدم مفاتيح الاسهم واضغط انتر لاختيار الشهر',\n      yearTablePrompt: 'استخدم مفاتيح الاسهم واضغط انتر لاختيار السنة',\n      selectDate: 'إختر التاريخ',\n      selectTime: 'إختر الوقت',\n      startDate: 'تاريخ البدء',\n      startTime: 'وقت البدء',\n      endDate: 'تاريخ الإنتهاء',\n      endTime: 'وقت الإنتهاء',\n      prevYear: 'السنة السابقة',\n      nextYear: 'السنة التالية',\n      prevMonth: 'الشهر السابق',\n      nextMonth: 'الشهر التالي',\n      year: 'سنة',\n      month1: 'كانون الثاني',\n      month2: 'شباط',\n      month3: 'اذار',\n      month4: 'نيسان',\n      month5: 'أيار',\n      month6: 'حزيران',\n      month7: 'تموز',\n      month8: 'اّب',\n      month9: 'ايلول',\n      month10: 'تشرين الاول',\n      month11: 'تشرين الثاني',\n      month12: 'كانون الاول',\n      week: 'أسبوع',\n      weeks: {\n        sun: 'الأحد',\n        mon: 'الأثنين',\n        tue: 'الثلاثاء',\n        wed: 'الأربعاء',\n        thu: 'الخميس',\n        fri: 'الجمعة',\n        sat: 'السبت',\n      },\n      months: {\n        jan: 'كانون الثاني',\n        feb: 'شباط',\n        mar: 'اذار',\n        apr: 'نيسان',\n        may: 'ايار',\n        jun: 'حزيران',\n        jul: 'تمور',\n        aug: 'اّب',\n        sep: 'ايلول',\n        oct: 'تشرين الاول',\n        nov: 'تشرين الثاني',\n        dec: 'كانون الاول',\n      },\n    },\n    inputNumber: {\n      decrease: 'طرح رقم',\n      increase: 'زيادة رقم',\n    },\n    select: {\n      loading: 'جار التحميل',\n      noMatch: 'لايوجد بيانات مطابقة',\n      noData: 'لايوجد بيانات',\n      placeholder: 'إختر',\n    },\n    mention: {\n      loading: 'جار التحميل',\n    },\n    dropdown: {\n      toggleDropdown: 'تبديل القائمة',\n    },\n    cascader: {\n      noMatch: 'لايوجد بيانات مطابقة',\n      loading: 'جار التحميل',\n      placeholder: 'إختر',\n      noData: 'لايوجد بيانات',\n    },\n    pagination: {\n      goto: 'أذهب إلى',\n      pagesize: '/صفحة',\n      total: 'الكل {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    dialog: {\n      close: 'أغلق هذا التبويب',\n    },\n    drawer: {\n      close: 'أغلق هذا التبويب',\n    },\n    messagebox: {\n      title: 'العنوان',\n      confirm: 'موافق',\n      cancel: 'إلغاء',\n      error: 'مدخل غير صحيح',\n      close: 'أغلق هذا التبويب',\n    },\n    upload: {\n      deleteTip: 'اضغط ازالة لحذف المحتوى',\n      delete: 'حذف',\n      preview: 'عرض',\n      continue: 'إستمرار',\n    },\n    table: {\n      emptyText: 'لايوجد بيانات',\n      confirmFilter: 'تأكيد',\n      resetFilter: 'حذف',\n      clearFilter: 'الكل',\n      sumText: 'المجموع',\n    },\n    tree: {\n      emptyText: 'لايوجد بيانات',\n    },\n    transfer: {\n      noMatch: 'لايوجد بيانات مطابقة',\n      noData: 'لايوجد بيانات',\n      titles: ['قائمة 1', 'قائمة 2'],\n      filterPlaceholder: 'ادخل كلمة',\n      noCheckedFormat: '{total} عناصر',\n      hasCheckedFormat: '{checked}/{total} مختار',\n    },\n    image: {\n      error: 'فشل',\n    },\n    pageHeader: {\n      title: 'عودة',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,YAAY,EAAE,yDAAyD;AAC7E,MAAM,WAAW,EAAE,oOAAoO;AACvP,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,0BAA0B;AACrC,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,eAAe,EAAE,oUAAoU;AAC3V,MAAM,gBAAgB,EAAE,kPAAkP;AAC1Q,MAAM,eAAe,EAAE,kPAAkP;AACzQ,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,UAAU,EAAE,yDAAyD;AAC3E,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,SAAS,EAAE,mDAAmD;AACpE,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,OAAO,EAAE,qEAAqE;AACpF,MAAM,QAAQ,EAAE,2EAA2E;AAC3F,MAAM,QAAQ,EAAE,2EAA2E;AAC3F,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,MAAM,EAAE,qEAAqE;AACnF,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,OAAO,EAAE,qEAAqE;AACpF,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,qEAAqE;AAClF,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,+DAA+D;AAC5E,QAAQ,GAAG,EAAE,qEAAqE;AAClF,QAAQ,GAAG,EAAE,+DAA+D;AAC5E,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,QAAQ,EAAE,mDAAmD;AACnE,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,OAAO,EAAE,gHAAgH;AAC/H,MAAM,MAAM,EAAE,2EAA2E;AACzF,MAAM,WAAW,EAAE,0BAA0B;AAC7C,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,+DAA+D;AAC9E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,2EAA2E;AACjG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gHAAgH;AAC/H,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,WAAW,EAAE,0BAA0B;AAC7C,MAAM,MAAM,EAAE,2EAA2E;AACzF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,6CAA6C;AACzD,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,wFAAwF;AACrG,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,wFAAwF;AACrG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,KAAK,EAAE,sEAAsE;AACnF,MAAM,KAAK,EAAE,wFAAwF;AACrG,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6HAA6H;AAC9I,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,QAAQ,EAAE,4CAA4C;AAC5D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,2EAA2E;AAC5F,MAAM,aAAa,EAAE,gCAAgC;AACrD,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,WAAW,EAAE,0BAA0B;AAC7C,MAAM,OAAO,EAAE,4CAA4C;AAC3D,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,2EAA2E;AAC5F,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gHAAgH;AAC/H,MAAM,MAAM,EAAE,2EAA2E;AACzF,MAAM,MAAM,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;AACtF,MAAM,iBAAiB,EAAE,mDAAmD;AAC5E,MAAM,eAAe,EAAE,wCAAwC;AAC/D,MAAM,gBAAgB,EAAE,kDAAkD;AAC1E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,0BAA0B;AACvC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}