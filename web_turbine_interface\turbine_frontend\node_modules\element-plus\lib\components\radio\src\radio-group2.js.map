{"version": 3, "file": "radio-group2.js", "sources": ["../../../../../../packages/components/radio/src/radio-group.vue"], "sourcesContent": ["<template>\n  <div\n    :id=\"groupId\"\n    ref=\"radioGroupRef\"\n    :class=\"ns.b('group')\"\n    role=\"radiogroup\"\n    :aria-label=\"!isLabeledByFormItem ? ariaLabel || 'radio-group' : undefined\"\n    :aria-labelledby=\"isLabeledByFormItem ? formItem!.labelId : undefined\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n  watch,\n} from 'vue'\nimport { useFormItem, useFormItemInputId } from '@element-plus/components/form'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { debugWarn } from '@element-plus/utils'\nimport { radioGroupEmits, radioGroupProps } from './radio-group'\nimport { radioGroupKey } from './constants'\n\nimport type { RadioGroupProps } from './radio-group'\n\ndefineOptions({\n  name: 'ElRadioGroup',\n})\n\nconst props = defineProps(radioGroupProps)\nconst emit = defineEmits(radioGroupEmits)\n\nconst ns = useNamespace('radio')\nconst radioId = useId()\nconst radioGroupRef = ref<HTMLDivElement>()\nconst { formItem } = useFormItem()\nconst { inputId: groupId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst changeEvent = (value: RadioGroupProps['modelValue']) => {\n  emit(UPDATE_MODEL_EVENT, value)\n  nextTick(() => emit(CHANGE_EVENT, value))\n}\n\nonMounted(() => {\n  const radios =\n    radioGroupRef.value!.querySelectorAll<HTMLInputElement>('[type=radio]')\n  const firstLabel = radios[0]\n  if (!Array.from(radios).some((radio) => radio.checked) && firstLabel) {\n    firstLabel.tabIndex = 0\n  }\n})\n\nconst name = computed(() => {\n  return props.name || radioId.value\n})\n\nprovide(\n  radioGroupKey,\n  reactive({\n    ...toRefs(props),\n    changeEvent,\n    name,\n  })\n)\n\nwatch(\n  () => props.modelValue,\n  () => {\n    if (props.validateEvent) {\n      formItem?.validate('change').catch((err) => debugWarn(err))\n    }\n  }\n)\n</script>\n"], "names": ["useNamespace", "useId", "ref", "useFormItem", "useFormItemInputId", "UPDATE_MODEL_EVENT", "nextTick", "CHANGE_EVENT", "onMounted", "computed", "provide", "radioGroupKey", "reactive", "toRefs", "watch", "debugWarn", "_createElementBlock", "_unref", "_normalizeClass"], "mappings": ";;;;;;;;;;;;;;uCAiCc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAKA,mBAAa,OAAO,CAAA,CAAA;AAC/B,IAAA,MAAM,UAAUC,aAAM,EAAA,CAAA;AACtB,IAAA,MAAM,gBAAgBC,OAAoB,EAAA,CAAA;AAC1C,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AACjC,IAAA,MAAM,EAAE,OAAS,EAAA,OAAA,EAAS,mBAAoB,EAAA,GAAIC,+BAAmB,KAAO,EAAA;AAAA,MAC1E,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,CAAC,KAAyC,KAAA;AAC5D,MAAA,IAAA,CAAKC,0BAAoB,KAAK,CAAA,CAAA;AAC9B,MAAAC,YAAA,CAAS,MAAM,IAAA,CAAKC,kBAAc,EAAA,KAAK,CAAC,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAAC,aAAA,CAAU,MAAM;AACd,MAAA,MAAM,MACJ,GAAA,aAAA,CAAc,KAAO,CAAA,gBAAA,CAAmC,cAAc,CAAA,CAAA;AACxE,MAAM,MAAA,UAAA,GAAa,OAAO,CAAC,CAAA,CAAA;AAC3B,MAAI,IAAA,CAAC,KAAM,CAAA,IAAA,CAAK,MAAM,CAAA,CAAE,IAAK,CAAA,CAAC,KAAU,KAAA,KAAA,CAAM,OAAO,CAAA,IAAK,UAAY,EAAA;AACpE,QAAA,UAAA,CAAW,QAAW,GAAA,CAAA,CAAA;AAAA,OACxB;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAOC,aAAS,MAAM;AAC1B,MAAO,OAAA,KAAA,CAAM,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAAC,WAAA,CAAAC,uBAAA,EAAAC,YAAA,CAAA;AAAA,MACE,GAAAC,UAAA,CAAA,KAAA,CAAA;AAAA,MACA,WAAS;AAAA,MACP,IAAA;AAAe,KACf,CAAA,CAAA,CAAA;AAAA,IACAC,SAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAAA,MACF,IAAC,KAAA,CAAA,aAAA,EAAA;AAAA,QACH,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAEA,OAAA;AAAA,KAAA,CACE;AAAY,IAAA,OACN,CAAA,IAAA,EAAA,MAAA,KAAA;AACJ,MAAA,oBAAyB,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AACvB,QAAU,EAAA,EAAAC,SAAA,CAAA,OAAA,CAAA;AAAgD,QAC5D,OAAA,EAAA,eAAA;AAAA,QACF,GAAA,EAAA,aAAA;AAAA,QACF,KAAA,EAAAC,kBAAA,CAAAD,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;;"}