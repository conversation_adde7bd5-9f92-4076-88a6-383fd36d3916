{"version": 3, "file": "use-date-table.js", "sources": ["../../../../../../packages/components/calendar/src/use-date-table.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport dayjs from 'dayjs'\nimport localeData from 'dayjs/plugin/localeData.js'\nimport { useLocale } from '@element-plus/hooks'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport { WEEK_DAYS } from '@element-plus/constants'\nimport { getMonthDays, getPrevMonthLastDays, toNestedArr } from './date-table'\n\nimport type { SetupContext } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type {\n  CalendarDateCell,\n  CalendarDateCellType,\n  DateTableEmits,\n  DateTableProps,\n} from './date-table'\n\nexport const useDateTable = (\n  props: DateTableProps,\n  emit: SetupContext<DateTableEmits>['emit']\n) => {\n  dayjs.extend(localeData)\n  // https://day.js.org/docs/en/i18n/locale-data\n  const firstDayOfWeek: number = dayjs.localeData().firstDayOfWeek()\n\n  const { t, lang } = useLocale()\n  const now = dayjs().locale(lang.value)\n\n  const isInRange = computed(() => !!props.range && !!props.range.length)\n\n  const rows = computed(() => {\n    let days: CalendarDateCell[] = []\n    if (isInRange.value) {\n      const [start, end] = props.range!\n      const currentMonthRange: CalendarDateCell[] = rangeArr(\n        end.date() - start.date() + 1\n      ).map((index) => ({\n        text: start.date() + index,\n        type: 'current',\n      }))\n\n      let remaining = currentMonthRange.length % 7\n      remaining = remaining === 0 ? 0 : 7 - remaining\n      const nextMonthRange: CalendarDateCell[] = rangeArr(remaining).map(\n        (_, index) => ({\n          text: index + 1,\n          type: 'next',\n        })\n      )\n      days = currentMonthRange.concat(nextMonthRange)\n    } else {\n      const firstDay = props.date.startOf('month').day()\n      const prevMonthDays: CalendarDateCell[] = getPrevMonthLastDays(\n        props.date,\n        (firstDay - firstDayOfWeek + 7) % 7\n      ).map((day) => ({\n        text: day,\n        type: 'prev',\n      }))\n      const currentMonthDays: CalendarDateCell[] = getMonthDays(props.date).map(\n        (day) => ({\n          text: day,\n          type: 'current',\n        })\n      )\n      days = [...prevMonthDays, ...currentMonthDays]\n      const remaining = 7 - (days.length % 7 || 7)\n      const nextMonthDays: CalendarDateCell[] = rangeArr(remaining).map(\n        (_, index) => ({\n          text: index + 1,\n          type: 'next',\n        })\n      )\n      days = days.concat(nextMonthDays)\n    }\n    return toNestedArr(days)\n  })\n\n  const weekDays = computed(() => {\n    const start = firstDayOfWeek\n    if (start === 0) {\n      return WEEK_DAYS.map((_) => t(`el.datepicker.weeks.${_}`))\n    } else {\n      return WEEK_DAYS.slice(start)\n        .concat(WEEK_DAYS.slice(0, start))\n        .map((_) => t(`el.datepicker.weeks.${_}`))\n    }\n  })\n\n  const getFormattedDate = (day: number, type: CalendarDateCellType): Dayjs => {\n    switch (type) {\n      case 'prev':\n        return props.date.startOf('month').subtract(1, 'month').date(day)\n      case 'next':\n        return props.date.startOf('month').add(1, 'month').date(day)\n      case 'current':\n        return props.date.date(day)\n    }\n  }\n\n  const handlePickDay = ({ text, type }: CalendarDateCell) => {\n    const date = getFormattedDate(text, type)\n    emit('pick', date)\n  }\n\n  const getSlotData = ({ text, type }: CalendarDateCell) => {\n    const day = getFormattedDate(text, type)\n    return {\n      isSelected: day.isSame(props.selectedDay),\n      type: `${type}-month`,\n      day: day.format('YYYY-MM-DD'),\n      date: day.toDate(),\n    }\n  }\n\n  return {\n    now,\n    isInRange,\n    rows,\n    weekDays,\n    getFormattedDate,\n    handlePickDay,\n    getSlotData,\n  }\n}\n"], "names": ["dayjs", "localeData", "useLocale", "computed", "rangeArr", "getPrevMonthLastDays", "getMonthDays", "toNestedArr", "WEEK_DAYS"], "mappings": ";;;;;;;;;;;;;;;;;AAOY,MAAC,YAAY,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAC7C,EAAEA,yBAAK,CAAC,MAAM,CAACC,8BAAU,CAAC,CAAC;AAC3B,EAAE,MAAM,cAAc,GAAGD,yBAAK,CAAC,UAAU,EAAE,CAAC,cAAc,EAAE,CAAC;AAC7D,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAGE,eAAS,EAAE,CAAC;AAClC,EAAE,MAAM,GAAG,GAAGF,yBAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,EAAE,MAAM,SAAS,GAAGG,YAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1E,EAAE,MAAM,IAAI,GAAGA,YAAQ,CAAC,MAAM;AAC9B,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AACvC,MAAM,MAAM,iBAAiB,GAAGC,cAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AACxF,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK;AAClC,QAAQ,IAAI,EAAE,SAAS;AACvB,OAAO,CAAC,CAAC,CAAC;AACV,MAAM,IAAI,SAAS,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;AACnD,MAAM,SAAS,GAAG,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;AACtD,MAAM,MAAM,cAAc,GAAGA,cAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM;AACpE,QAAQ,IAAI,EAAE,KAAK,GAAG,CAAC;AACvB,QAAQ,IAAI,EAAE,MAAM;AACpB,OAAO,CAAC,CAAC,CAAC;AACV,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AACtD,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;AACzD,MAAM,MAAM,aAAa,GAAGC,8BAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,QAAQ,GAAG,cAAc,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AAChH,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE,MAAM;AACpB,OAAO,CAAC,CAAC,CAAC;AACV,MAAM,MAAM,gBAAgB,GAAGC,sBAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACtE,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE,SAAS;AACvB,OAAO,CAAC,CAAC,CAAC;AACV,MAAM,IAAI,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,gBAAgB,CAAC,CAAC;AACrD,MAAM,MAAM,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD,MAAM,MAAM,aAAa,GAAGF,cAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM;AACnE,QAAQ,IAAI,EAAE,KAAK,GAAG,CAAC;AACvB,QAAQ,IAAI,EAAE,MAAM;AACpB,OAAO,CAAC,CAAC,CAAC;AACV,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,OAAOG,qBAAW,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGJ,YAAQ,CAAC,MAAM;AAClC,IAAI,MAAM,KAAK,GAAG,cAAc,CAAC;AACjC,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE;AACrB,MAAM,OAAOK,cAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,KAAK,MAAM;AACX,MAAM,OAAOA,cAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAACA,cAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChH,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;AAC1C,IAAI,QAAQ,IAAI;AAChB,MAAM,KAAK,MAAM;AACjB,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1E,MAAM,KAAK,MAAM;AACjB,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrE,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;AAC5C,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;AAC1C,IAAI,MAAM,GAAG,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,OAAO;AACX,MAAM,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC;AAC/C,MAAM,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;AAC3B,MAAM,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;AACnC,MAAM,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE;AACxB,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,GAAG;AACP,IAAI,SAAS;AACb,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}