{"version": 3, "file": "bg.mjs", "sources": ["../../../../../packages/locale/lang/bg.ts"], "sourcesContent": ["export default {\n  name: 'bg',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Изчисти',\n    },\n    datepicker: {\n      now: 'Сега',\n      today: 'Днес',\n      cancel: 'Откаж<PERSON>',\n      clear: 'Изчисти',\n      confirm: 'ОК',\n      selectDate: 'Избери дата',\n      selectTime: 'Избери час',\n      startDate: 'Начална дата',\n      startTime: 'Начален час',\n      endDate: 'Крайна дата',\n      endTime: 'Краен час',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: '',\n      month1: 'Януари',\n      month2: 'Февруари',\n      month3: 'Март',\n      month4: 'Април',\n      month5: 'Май',\n      month6: 'Юни',\n      month7: 'Юли',\n      month8: 'Август',\n      month9: 'Септември',\n      month10: 'Октомври',\n      month11: 'Ноември',\n      month12: 'Декември',\n      // week: 'Седмица',\n      weeks: {\n        sun: 'Нед',\n        mon: 'Пон',\n        tue: 'Вто',\n        wed: 'Сря',\n        thu: 'Чет',\n        fri: 'Пет',\n        sat: 'Съб',\n      },\n      months: {\n        jan: 'Яну',\n        feb: 'Фев',\n        mar: 'Мар',\n        apr: 'Апр',\n        may: 'Май',\n        jun: 'Юни',\n        jul: 'Юли',\n        aug: 'Авг',\n        sep: 'Сеп',\n        oct: 'Окт',\n        nov: 'Ное',\n        dec: 'Дек',\n      },\n    },\n    select: {\n      loading: 'Зареждане',\n      noMatch: 'Няма намерени',\n      noData: 'Няма данни',\n      placeholder: 'Избери',\n    },\n    mention: {\n      loading: 'Зареждане',\n    },\n    cascader: {\n      noMatch: 'Няма намерени',\n      loading: 'Зареждане',\n      placeholder: 'Избери',\n      noData: 'Няма данни',\n    },\n    pagination: {\n      goto: 'Иди на',\n      pagesize: '/страница',\n      total: 'Общо {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Съобщение',\n      confirm: 'ОК',\n      cancel: 'Откажи',\n      error: 'Невалидни данни',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Изтрий',\n      preview: 'Прегледай',\n      continue: 'Продължи',\n    },\n    table: {\n      emptyText: 'Няма данни',\n      confirmFilter: 'Потвърди',\n      resetFilter: 'Изчисти',\n      clearFilter: 'Всички',\n      sumText: 'Sum', // to be translated\n    },\n    tree: {\n      emptyText: 'Няма данни',\n    },\n    transfer: {\n      noMatch: 'Няма намерени',\n      noData: 'Няма данни',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'Enter keyword', // to be translated\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,0BAA0B;AACrC,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,UAAU,EAAE,+DAA+D;AACjF,MAAM,UAAU,EAAE,yDAAyD;AAC3E,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,OAAO,EAAE,+DAA+D;AAC9E,MAAM,OAAO,EAAE,mDAAmD;AAClE,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,wDAAwD;AACtE,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,WAAW,EAAE,sCAAsC;AACzD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,wDAAwD;AACvE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,MAAM,EAAE,yDAAyD;AACvE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,iCAAiC;AAC7C,MAAM,QAAQ,EAAE,mDAAmD;AACnE,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,wDAAwD;AACrE,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,uFAAuF;AACpG,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,QAAQ,EAAE,kDAAkD;AAClE,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,aAAa,EAAE,kDAAkD;AACvE,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,yDAAyD;AAC1E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,2EAA2E;AAC1F,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClC,MAAM,iBAAiB,EAAE,eAAe;AACxC,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}