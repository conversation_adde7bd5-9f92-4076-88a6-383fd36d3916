{"version": 3, "file": "te.min.mjs", "sources": ["../../../../packages/locale/lang/te.ts"], "sourcesContent": ["export default {\n  name: 'te',\n  el: {\n    breadcrumb: {\n      label: 'బ్రెడ్‌క్రంబ్',\n    },\n    colorpicker: {\n      confirm: 'సరే',\n      clear: 'తొలగించు',\n      defaultLabel: 'రంగు ఎంచుకోండి',\n      description:\n        'ప్రస్తుత రంగు {color}. కొత్త రంగును ఎంచుకోవడానికి ఎంటర్ నొక్కండి.',\n      alphaLabel: 'అల్ఫా విలువను ఎంచుకోండి',\n    },\n    datepicker: {\n      now: 'ఇప్పుడు',\n      today: 'ఈ రోజు',\n      cancel: 'రద్దు',\n      clear: 'తొలగించు',\n      confirm: 'సరే',\n      dateTablePrompt:\n        'నెలలోని రోజును ఎంచుకోవడానికి ఈతలు మరియు ఎంటర్ ఉపయోగించండి',\n      monthTablePrompt: 'నెలను ఎంచుకోవడానికి ఈతలు మరియు ఎంటర్ ఉపయోగించండి',\n      yearTablePrompt:\n        'సంవత్సరాన్ని ఎంచుకోవడానికి ఈతలు మరియు ఎంటర్ ఉపయోగించండి',\n      selectedDate: 'ఎంచుకున్న తేదీ',\n      selectDate: 'తేదీ ఎంచుకోండి',\n      selectTime: 'సమయం ఎంచుకోండి',\n      startDate: 'ప్రారంభ తేదీ',\n      startTime: 'ప్రారంభ సమయం',\n      endDate: 'ముగింపు తేదీ',\n      endTime: 'ముగింపు సమయం',\n      prevYear: 'గత సంవత్సరం',\n      nextYear: 'తదుపరి సంవత్సరం',\n      prevMonth: 'గత నెల',\n      nextMonth: 'తదుపరి నెల',\n      year: 'సంవత్సరం',\n      month1: 'జనవరి',\n      month2: 'ఫిబ్రవరి',\n      month3: 'మార్చి',\n      month4: 'ఏప్రిల్',\n      month5: 'మే',\n      month6: 'జూన్',\n      month7: 'జూలై',\n      month8: 'ఆగస్టు',\n      month9: 'సెప్టెంబర్',\n      month10: 'అక్టోబర్',\n      month11: 'నవంబర్',\n      month12: 'డిసెంబర్',\n      week: 'వారం',\n      weeks: {\n        sun: 'ఆది',\n        mon: 'సోమ',\n        tue: 'మంగళ',\n        wed: 'బుధ',\n        thu: 'గురు',\n        fri: 'శుక్ర',\n        sat: 'శని',\n      },\n      weeksFull: {\n        sun: 'ఆదివారం',\n        mon: 'సోమవారం',\n        tue: 'మంగళవారం',\n        wed: 'బుధవారం',\n        thu: 'గురువారం',\n        fri: 'శుక్రవారం',\n        sat: 'శనివారం',\n      },\n      months: {\n        jan: 'జన',\n        feb: 'ఫిబ్ర',\n        mar: 'మార్చి',\n        apr: 'ఏప్రి',\n        may: 'మే',\n        jun: 'జూన్',\n        jul: 'జూలై',\n        aug: 'ఆగ',\n        sep: 'సెప్',\n        oct: 'అక్టో',\n        nov: 'నవం',\n        dec: 'డిసెం',\n      },\n    },\n    inputNumber: {\n      decrease: 'సంఖ్య తగ్గించు',\n      increase: 'సంఖ్య పెంచు',\n    },\n    select: {\n      loading: 'లోడ్ అవుతోంది',\n      noMatch: 'ఫలితాలు కనబడలేదు',\n      noData: 'డేటా లేదు',\n      placeholder: 'ఎంచుకోండి',\n    },\n    mention: {\n      loading: 'లోడ్ అవుతోంది',\n    },\n    dropdown: {\n      toggleDropdown: 'డ్రాప్‌డౌన్ మార్చు',\n    },\n    cascader: {\n      noMatch: 'ఫలితాలు కనబడలేదు',\n      loading: 'లోడ్ అవుతోంది',\n      placeholder: 'ఎంచుకోండి',\n      noData: 'డేటా లేదు',\n    },\n    pagination: {\n      goto: 'వెళ్ళండి',\n      pagesize: '/పేజీ',\n      total: 'మొత్తం {total}',\n      pageClassifier: '',\n      page: 'పేజీ',\n      prev: 'మునుపటి పేజీకి వెళ్ళండి',\n      next: 'తదుపరి పేజీకి వెళ్ళండి',\n      currentPage: 'పేజీ {pager}',\n      prevPages: 'మునుపటి {pager} పేజీలు',\n      nextPages: 'తదుపరి {pager} పేజీలు',\n      deprecationWarning:\n        'పాత పద్ధతులు గుర్తించబడ్డాయి, మరిన్ని వివరాల కోసం el-pagination డాక్యుమెంటేషన్ చూడండి',\n    },\n    dialog: {\n      close: 'ఈ డైలాగ్ మూసివేయి',\n    },\n    drawer: {\n      close: 'ఈ డైలాగ్ మూసివేయి',\n    },\n    messagebox: {\n      title: 'సందేశం',\n      confirm: 'సరే',\n      cancel: 'రద్దు',\n      error: 'చెల్లని ఇన్‌పుట్',\n      close: 'ఈ డైలాగ్ మూసివేయి',\n    },\n    upload: {\n      deleteTip: 'తొలగించడానికి డిలీట్ నొక్కండి',\n      delete: 'తొలగించు',\n      preview: 'ప్రివ్యూ',\n      continue: 'కొనసాగించు',\n    },\n    slider: {\n      defaultLabel: '{min} మరియు {max} మధ్య స్లైడర్',\n      defaultRangeStartLabel: 'ప్రారంభ విలువ ఎంచుకోండి',\n      defaultRangeEndLabel: 'ముగింపు విలువ ఎంచుకోండి',\n    },\n    table: {\n      emptyText: 'డేటా లేదు',\n      confirmFilter: 'నిర్ధారించు',\n      resetFilter: 'రీసెట్',\n      clearFilter: 'తొలగించు',\n      sumText: 'మొత్తం',\n    },\n    tour: {\n      next: 'తదుపరి',\n      previous: 'గత',\n      finish: 'ముగించు',\n    },\n    tree: {\n      emptyText: 'డేటా లేదు',\n    },\n    transfer: {\n      noMatch: 'ఫలితాలు కనబడలేదు',\n      noData: 'డేటా లేదు',\n      titles: ['జాబితా 1', 'జాబితా 2'],\n      filterPlaceholder: 'కీవర్డ్ నమోదు చేయండి',\n      noCheckedFormat: '{total} అంశాలు',\n      hasCheckedFormat: '{checked}/{total} ఎంపిక చేయబడ్డాయి',\n    },\n    image: {\n      error: 'విఫలమైంది',\n    },\n    pageHeader: {\n      title: 'వెనక్కి',\n    },\n    popconfirm: {\n      confirmButtonText: 'అవును',\n      cancelButtonText: 'కాదు',\n    },\n    carousel: {\n      leftArrow: 'క్యారసెల్ ఎడమ బాణం',\n      rightArrow: 'క్యారసెల్ కుడి బాణం',\n      indicator: 'క్యారసెల్ సూచిక {index} కి మార్చు',\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gFAAgF,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,kDAAkD,CAAC,YAAY,CAAC,iFAAiF,CAAC,WAAW,CAAC,wTAAwT,CAAC,UAAU,CAAC,kIAAkI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,KAAK,CAAC,iCAAiC,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,kDAAkD,CAAC,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,0TAA0T,CAAC,gBAAgB,CAAC,yQAAyQ,CAAC,eAAe,CAAC,mTAAmT,CAAC,YAAY,CAAC,iFAAiF,CAAC,UAAU,CAAC,iFAAiF,CAAC,UAAU,CAAC,iFAAiF,CAAC,SAAS,CAAC,qEAAqE,CAAC,SAAS,CAAC,qEAAqE,CAAC,OAAO,CAAC,qEAAqE,CAAC,OAAO,CAAC,qEAAqE,CAAC,QAAQ,CAAC,+DAA+D,CAAC,QAAQ,CAAC,uFAAuF,CAAC,SAAS,CAAC,iCAAiC,CAAC,SAAS,CAAC,yDAAyD,CAAC,IAAI,CAAC,kDAAkD,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,8DAA8D,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,kDAAkD,CAAC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,wDAAwD,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,iFAAiF,CAAC,QAAQ,CAAC,+DAA+D,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,2EAA2E,CAAC,OAAO,CAAC,6FAA6F,CAAC,MAAM,CAAC,mDAAmD,CAAC,WAAW,CAAC,wDAAwD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,2EAA2E,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,yGAAyG,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6FAA6F,CAAC,OAAO,CAAC,2EAA2E,CAAC,WAAW,CAAC,wDAAwD,CAAC,MAAM,CAAC,mDAAmD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,kDAAkD,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,8CAA8C,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,kIAAkI,CAAC,IAAI,CAAC,4HAA4H,CAAC,WAAW,CAAC,kCAAkC,CAAC,SAAS,CAAC,yFAAyF,CAAC,SAAS,CAAC,mFAAmF,CAAC,kBAAkB,CAAC,kZAAkZ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,8FAA8F,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,8FAA8F,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,6FAA6F,CAAC,KAAK,CAAC,8FAA8F,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,sKAAsK,CAAC,MAAM,CAAC,kDAAkD,CAAC,OAAO,CAAC,kDAAkD,CAAC,QAAQ,CAAC,8DAA8D,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,gHAAgH,CAAC,sBAAsB,CAAC,kIAAkI,CAAC,oBAAoB,CAAC,kIAAkI,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,aAAa,CAAC,oEAAoE,CAAC,WAAW,CAAC,sCAAsC,CAAC,WAAW,CAAC,kDAAkD,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,4CAA4C,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6FAA6F,CAAC,MAAM,CAAC,mDAAmD,CAAC,MAAM,CAAC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,CAAC,iBAAiB,CAAC,gHAAgH,CAAC,eAAe,CAAC,8CAA8C,CAAC,gBAAgB,CAAC,+GAA+G,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,oGAAoG,CAAC,UAAU,CAAC,0GAA0G,CAAC,SAAS,CAAC,iJAAiJ,CAAC,CAAC,CAAC;;;;"}