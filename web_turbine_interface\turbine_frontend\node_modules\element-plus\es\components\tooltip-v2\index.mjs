import TooltipV2 from './src/tooltip.mjs';
export { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './src/arrow2.mjs';
export { tooltipV2ContentProps } from './src/content.mjs';
export { tooltipV2RootProps } from './src/root.mjs';
export { tooltipV2Props } from './src/tooltip2.mjs';
export { tooltipV2TriggerProps } from './src/trigger.mjs';
export { TOOLTIP_V2_OPEN, tooltipV2ContentKey, tooltipV2RootKey } from './src/constants.mjs';
import { withInstall } from '../../utils/vue/install.mjs';

const ElTooltipV2 = withInstall(TooltipV2);

export { ElTooltipV2, ElTooltipV2 as default };
//# sourceMappingURL=index.mjs.map
