{"version": 3, "file": "vi.min.js", "sources": ["../../../../packages/locale/lang/vi.ts"], "sourcesContent": ["export default {\n  name: 'vi',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON>óa',\n    },\n    datepicker: {\n      now: 'Hiện tại',\n      today: 'Hôm nay',\n      cancel: '<PERSON>ủy',\n      clear: '<PERSON>óa',\n      confirm: 'OK',\n      selectDate: 'Chọn ngày',\n      selectTime: 'Chọn giờ',\n      startDate: '<PERSON><PERSON>y bắt đầu',\n      startTime: 'Thời gian bắt đầu',\n      endDate: '<PERSON><PERSON><PERSON> kết thúc',\n      endTime: 'Thời gian kết thúc',\n      prevYear: 'Năm trước',\n      nextYear: 'Năm tới',\n      prevMonth: 'Tháng trước',\n      nextMonth: 'Tháng tới',\n      year: 'Năm',\n      month1: 'Tháng 1',\n      month2: 'Tháng 2',\n      month3: 'Tháng 3',\n      month4: 'Tháng 4',\n      month5: 'Tháng 5',\n      month6: 'Tháng 6',\n      month7: 'Tháng 7',\n      month8: 'Tháng 8',\n      month9: 'Tháng 9',\n      month10: 'Tháng 10',\n      month11: 'Tháng 11',\n      month12: 'Tháng 12',\n      // week: 'week',\n      weeks: {\n        sun: 'CN',\n        mon: 'T2',\n        tue: 'T3',\n        wed: 'T4',\n        thu: 'T5',\n        fri: 'T6',\n        sat: 'T7',\n      },\n      months: {\n        jan: 'Th.1',\n        feb: 'Th.2',\n        mar: 'Th.3',\n        apr: 'Th.4',\n        may: 'Th.5',\n        jun: 'Th.6',\n        jul: 'Th.7',\n        aug: 'Th.8',\n        sep: 'Th.9',\n        oct: 'Th.10',\n        nov: 'Th.11',\n        dec: 'Th.12',\n      },\n    },\n    select: {\n      loading: 'Đang tải',\n      noMatch: 'Dữ liệu không phù hợp',\n      noData: 'Không tìm thấy dữ liệu',\n      placeholder: 'Chọn',\n    },\n    mention: {\n      loading: 'Đang tải',\n    },\n    cascader: {\n      noMatch: 'Dữ liệu không phù hợp',\n      loading: 'Đang tải',\n      placeholder: 'Chọn',\n      noData: 'Không tìm thấy dữ liệu',\n    },\n    pagination: {\n      goto: 'Nhảy tới',\n      pagesize: '/trang',\n      total: 'Tổng {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Thông báo',\n      confirm: 'OK',\n      cancel: 'Hủy',\n      error: 'Dữ liệu không hợp lệ',\n    },\n    upload: {\n      deleteTip: 'Nhấn xoá để xoá',\n      delete: 'Xóa',\n      preview: 'Xem trước',\n      continue: 'Tiếp tục',\n    },\n    table: {\n      emptyText: 'Không có dữ liệu',\n      confirmFilter: 'Xác nhận',\n      resetFilter: 'Làm mới',\n      clearFilter: 'Xóa hết',\n      sumText: 'Tổng',\n    },\n    tour: {\n      next: 'Tiếp',\n      previous: 'Trước',\n      finish: 'Hoàn thành',\n    },\n    tree: {\n      emptyText: 'Không có dữ liệu',\n    },\n    transfer: {\n      noMatch: 'Dữ liệu không phù hợp',\n      noData: 'Không tìm thấy dữ liệu',\n      titles: ['Danh sách 1', 'Danh sách 2'],\n      filterPlaceholder: 'Nhập từ khóa',\n      noCheckedFormat: '{total} mục',\n      hasCheckedFormat: '{checked}/{total} đã chọn ',\n    },\n    image: {\n      error: 'LỖI',\n    },\n    pageHeader: {\n      title: 'Quay lại',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ok',\n      cancelButtonText: 'Huỷ',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,oBAAoB,CAAC,SAAS,CAAC,gCAAgC,CAAC,SAAS,CAAC,uCAAuC,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,iCAAiC,CAAC,QAAQ,CAAC,0BAA0B,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,0BAA0B,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,4CAA4C,CAAC,MAAM,CAAC,6CAA6C,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,6CAA6C,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,sCAAsC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,aAAa,CAAC,kBAAkB,CAAC,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,MAAM,CAAC,6CAA6C,CAAC,MAAM,CAAC,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,2BAA2B,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,yCAAyC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}