'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tooltip$1 = require('./src/tooltip.js');
var arrow = require('./src/arrow2.js');
var content = require('./src/content.js');
var root = require('./src/root.js');
var tooltip = require('./src/tooltip2.js');
var trigger = require('./src/trigger.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElTooltipV2 = install.withInstall(tooltip$1["default"]);

exports.tooltipV2ArrowProps = arrow.tooltipV2ArrowProps;
exports.tooltipV2ArrowSpecialProps = arrow.tooltipV2ArrowSpecialProps;
exports.tooltipV2ContentProps = content.tooltipV2ContentProps;
exports.tooltipV2RootProps = root.tooltipV2RootProps;
exports.tooltipV2Props = tooltip.tooltipV2Props;
exports.tooltipV2TriggerProps = trigger.tooltipV2TriggerProps;
exports.TOOLTIP_V2_OPEN = constants.TOOLTIP_V2_OPEN;
exports.tooltipV2ContentKey = constants.tooltipV2ContentKey;
exports.tooltipV2RootKey = constants.tooltipV2RootKey;
exports.ElTooltipV2 = ElTooltipV2;
exports["default"] = ElTooltipV2;
//# sourceMappingURL=index.js.map
