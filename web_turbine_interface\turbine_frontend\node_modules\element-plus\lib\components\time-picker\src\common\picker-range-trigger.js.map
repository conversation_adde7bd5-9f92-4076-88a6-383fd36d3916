{"version": 3, "file": "picker-range-trigger.js", "sources": ["../../../../../../../packages/components/time-picker/src/common/picker-range-trigger.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"wrapperRef\"\n    :class=\"[nsDate.is('active', isFocused), $attrs.class]\"\n    :style=\"($attrs.style as CSSProperties)\"\n    @click=\"handleClick\"\n    @mouseenter=\"handleMouseEnter\"\n    @mouseleave=\"handleMouseLeave\"\n    @touchstart.passive=\"handleTouchStart\"\n  >\n    <slot name=\"prefix\" />\n    <input\n      v-bind=\"attrs\"\n      :id=\"id && id[0]\"\n      ref=\"inputRef\"\n      :name=\"name && name[0]\"\n      :placeholder=\"startPlaceholder\"\n      :value=\"modelValue && modelValue[0]\"\n      :class=\"nsRange.b('input')\"\n      :disabled=\"disabled\"\n      @input=\"handleStartInput\"\n      @change=\"handleStartChange\"\n    />\n    <slot name=\"range-separator\" />\n    <input\n      v-bind=\"attrs\"\n      :id=\"id && id[1]\"\n      ref=\"endInputRef\"\n      :name=\"name && name[1]\"\n      :placeholder=\"endPlaceholder\"\n      :value=\"modelValue && modelValue[1]\"\n      :class=\"nsRange.b('input')\"\n      :disabled=\"disabled\"\n      @input=\"handleEndInput\"\n      @change=\"handleEndChange\"\n    />\n    <slot name=\"suffix\" />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport { useAttrs, useFocusController, useNamespace } from '@element-plus/hooks'\nimport { timePickerRangeTriggerProps } from './props'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'PickerRangeTrigger',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(timePickerRangeTriggerProps)\nconst emit = defineEmits([\n  'mouseenter',\n  'mouseleave',\n  'click',\n  'touchstart',\n  'focus',\n  'blur',\n  'startInput',\n  'endInput',\n  'startChange',\n  'endChange',\n])\n\nconst attrs = useAttrs()\nconst nsDate = useNamespace('date')\nconst nsRange = useNamespace('range')\n\nconst inputRef = ref<HTMLInputElement>()\nconst endInputRef = ref<HTMLInputElement>()\n\nconst { wrapperRef, isFocused } = useFocusController(inputRef, {\n  disabled: computed(() => props.disabled),\n})\n\nconst handleClick = (evt: MouseEvent) => {\n  emit('click', evt)\n}\n\nconst handleMouseEnter = (evt: MouseEvent) => {\n  emit('mouseenter', evt)\n}\n\nconst handleMouseLeave = (evt: MouseEvent) => {\n  emit('mouseleave', evt)\n}\n\nconst handleTouchStart = (evt: TouchEvent) => {\n  emit('mouseenter', evt)\n}\n\nconst handleStartInput = (evt: Event) => {\n  emit('startInput', evt)\n}\n\nconst handleEndInput = (evt: Event) => {\n  emit('endInput', evt)\n}\n\nconst handleStartChange = (evt: Event) => {\n  emit('startChange', evt)\n}\n\nconst handleEndChange = (evt: Event) => {\n  emit('endChange', evt)\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n  endInputRef.value?.blur()\n}\n\ndefineExpose({\n  focus,\n  blur,\n})\n</script>\n"], "names": ["useAttrs", "useNamespace", "ref", "useFocusController", "computed"], "mappings": ";;;;;;;;;;;uCA+Cc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;AAgBA,IAAA,MAAM,QAAQA,cAAS,EAAA,CAAA;AACvB,IAAM,MAAA,MAAA,GAASC,qBAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,OAAA,GAAUA,qBAAa,OAAO,CAAA,CAAA;AAEpC,IAAA,MAAM,WAAWC,OAAsB,EAAA,CAAA;AACvC,IAAA,MAAM,cAAcA,OAAsB,EAAA,CAAA;AAE1C,IAAA,MAAM,EAAE,UAAA,EAAY,SAAU,EAAA,GAAIC,2BAAmB,QAAU,EAAA;AAAA,MAC7D,QAAU,EAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,QAAQ,CAAA;AAAA,KACxC,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,CAAC,GAAoB,KAAA;AACvC,MAAA,IAAA,CAAK,SAAS,GAAG,CAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,GAAoB,KAAA;AAC5C,MAAA,IAAA,CAAK,cAAc,GAAG,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,GAAoB,KAAA;AAC5C,MAAA,IAAA,CAAK,cAAc,GAAG,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,GAAoB,KAAA;AAC5C,MAAA,IAAA,CAAK,cAAc,GAAG,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,GAAe,KAAA;AACvC,MAAA,IAAA,CAAK,cAAc,GAAG,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,GAAe,KAAA;AACrC,MAAA,IAAA,CAAK,YAAY,GAAG,CAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,GAAe,KAAA;AACxC,MAAA,IAAA,CAAK,eAAe,GAAG,CAAA,CAAA;AAAA,KACzB,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,GAAe,KAAA;AACtC,MAAA,IAAA,CAAK,aAAa,GAAG,CAAA,CAAA;AAAA,KACvB,CAAA;AAEA,IAAA,MAAM,QAAQ,MAAM;AAClB,MAAA,IAAA,EAAA,CAAA;AAAsB,MACxB,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,IAAA,SAAqB;AACrB,MAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AAAwB,MAC1B,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAEA,MAAa,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA,KACX,CAAA;AAAA,IACA,MAAA,CAAA;AAAA,MACD,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}