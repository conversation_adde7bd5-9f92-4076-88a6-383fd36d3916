{"version": 3, "file": "tab-pane.mjs", "sources": ["../../../../../../packages/components/tabs/src/tab-pane.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type TabPane from './tab-pane.vue'\n\nexport const tabPaneProps = buildProps({\n  /**\n   * @description title of the tab\n   */\n  label: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description identifier corresponding to the name of Tabs, representing the alias of the tab-pane, the default is ordinal number of the tab-pane in the sequence, e.g. the first tab-pane is '0'\n   */\n  name: {\n    type: [String, Number],\n  },\n  /**\n   * @description whether Tab is closable\n   */\n  closable: Boolean,\n  /**\n   * @description whether Tab is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether Tab is lazily rendered\n   */\n  lazy: Boolean,\n} as const)\n\nexport type TabPaneProps = ExtractPropTypes<typeof tabPaneProps>\nexport type TabPanePropsPublic = __ExtractPublicPropTypes<typeof tabPaneProps>\n\nexport type TabPaneInstance = InstanceType<typeof TabPane> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,YAAY,GAAG,UAAU,CAAC;AACvC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE,OAAO;AACf,CAAC;;;;"}