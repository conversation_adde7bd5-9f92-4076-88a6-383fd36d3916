{"version": 3, "file": "carousel-item.js", "sources": ["../../../../../../packages/components/carousel/src/carousel-item.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const carouselItemProps = buildProps({\n  /**\n   * @description name of the item, can be used in `setActiveItem`\n   */\n  name: { type: String, default: '' },\n  /**\n   * @description text content for the corresponding indicator\n   */\n  label: {\n    type: [String, Number],\n    default: '',\n  },\n} as const)\n\nexport type CarouselItemProps = ExtractPropTypes<typeof carouselItemProps>\nexport type CarouselItemPropsPublic = __ExtractPublicPropTypes<\n  typeof carouselItemProps\n>\n"], "names": ["buildProps"], "mappings": ";;;;;;AACY,MAAC,iBAAiB,GAAGA,kBAAU,CAAC;AAC5C,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE;AACrC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;"}