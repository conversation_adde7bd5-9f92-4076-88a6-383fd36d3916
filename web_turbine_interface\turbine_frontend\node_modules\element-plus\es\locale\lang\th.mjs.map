{"version": 3, "file": "th.mjs", "sources": ["../../../../../packages/locale/lang/th.ts"], "sourcesContent": ["export default {\n  name: 'th',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'ตกลง',\n      clear: 'ล้างข้อมูล',\n    },\n    datepicker: {\n      now: 'ตอนนี้',\n      today: 'วันนี้',\n      cancel: 'ยกเลิก',\n      clear: 'ล้างข้อมูล',\n      confirm: 'ตกลง',\n      selectDate: 'เลือกวันที่',\n      selectTime: 'เลือกเวลา',\n      startDate: 'วันที่เริ่มต้น',\n      startTime: 'เวลาเริ่มต้น',\n      endDate: 'วันที่สิ้นสุด',\n      endTime: 'เวลาสิ้นสุด',\n      prevYear: 'ปีก่อนหน้า',\n      nextYear: 'ปีถัดไป',\n      prevMonth: 'เดือนก่อนหน้า',\n      nextMonth: 'เดือนถัดไป',\n      year: 'ปี',\n      month1: 'มกราคม',\n      month2: 'กุมภาพันธ์',\n      month3: 'มีนาคม',\n      month4: 'เมษายน',\n      month5: 'พฤษภาคม',\n      month6: 'มิถุนายน',\n      month7: 'กรกฎาคม',\n      month8: 'สิงหาคม',\n      month9: 'กันยายน',\n      month10: 'ตุลาคม',\n      month11: 'พฤศจิกายน',\n      month12: 'ธันวาคม',\n      // week: 'week',\n      weeks: {\n        sun: 'อา',\n        mon: 'จ',\n        tue: 'อ',\n        wed: 'พ',\n        thu: 'พฤ',\n        fri: 'ศ',\n        sat: 'ส',\n      },\n      months: {\n        jan: 'ม.ค.',\n        feb: 'ก.พ.',\n        mar: 'มี.ค.',\n        apr: 'เม.ย.',\n        may: 'พ.ค.',\n        jun: 'มิ.ย.',\n        jul: 'ก.ค.',\n        aug: 'ส.ค.',\n        sep: 'ก.ย.',\n        oct: 'ต.ค.',\n        nov: 'พ.ย.',\n        dec: 'ธ.ค.',\n      },\n    },\n    select: {\n      loading: 'กำลังโหลด',\n      noMatch: 'ไม่พบข้อมูลที่ตรงกัน',\n      noData: 'ไม่พบข้อมูล',\n      placeholder: 'เลือก',\n    },\n    mention: {\n      loading: 'กำลังโหลด',\n    },\n    cascader: {\n      noMatch: 'ไม่พบข้อมูลที่ตรงกัน',\n      loading: 'กำลังโหลด',\n      placeholder: 'เลือก',\n      noData: 'ไม่พบข้อมูล',\n    },\n    pagination: {\n      goto: 'ไปที่',\n      pagesize: '/หน้า',\n      total: 'ทั้งหมด {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'ข้อความ',\n      confirm: 'ตกลง',\n      cancel: 'ยกเลิก',\n      error: 'คุณป้อนข้อมูลไม่ถูกต้อง',\n    },\n    upload: {\n      deleteTip: 'กดปุ่ม \"ลบ\" เพื่อลบออก',\n      delete: 'ลบ',\n      preview: 'ตัวอย่าง',\n      continue: 'ทำต่อ',\n    },\n    table: {\n      emptyText: 'ไม่พบข้อมูล',\n      confirmFilter: 'ยืนยัน',\n      resetFilter: 'รีเซ็ต',\n      clearFilter: 'ทั้งหมด',\n      sumText: 'รวม',\n    },\n    tour: {\n      next: 'ถัดไป',\n      previous: 'ย้อนกลับ',\n      finish: 'เสร็จสิ้น',\n    },\n    tree: {\n      emptyText: 'ไม่พบข้อมูล',\n    },\n    transfer: {\n      noMatch: 'ไม่พบข้อมูลที่ตรงกัน',\n      noData: 'ไม่พบข้อมูล',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'กรอกคีย์เวิร์ด',\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'ย้อนกลับ',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,KAAK,EAAE,8DAA8D;AAC3E,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,sCAAsC;AACjD,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,8DAA8D;AAC3E,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,UAAU,EAAE,oEAAoE;AACtF,MAAM,UAAU,EAAE,wDAAwD;AAC1E,MAAM,SAAS,EAAE,sFAAsF;AACvG,MAAM,SAAS,EAAE,0EAA0E;AAC3F,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,OAAO,EAAE,oEAAoE;AACnF,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,MAAM,QAAQ,EAAE,4CAA4C;AAC5D,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,SAAS,EAAE,8DAA8D;AAC/E,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,8DAA8D;AAC5E,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,sBAAsB;AACnC,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,QAAQ,GAAG,EAAE,gBAAgB;AAC7B,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,MAAM,EAAE,oEAAoE;AAClF,MAAM,WAAW,EAAE,gCAAgC;AACnD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,wDAAwD;AACvE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,MAAM,EAAE,oEAAoE;AAClF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,QAAQ,EAAE,2BAA2B;AAC3C,MAAM,KAAK,EAAE,oDAAoD;AACjE,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,4IAA4I;AACzJ,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,kHAAkH;AACnI,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,QAAQ,EAAE,gCAAgC;AAChD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,oEAAoE;AACrF,MAAM,aAAa,EAAE,sCAAsC;AAC3D,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,OAAO,EAAE,oBAAoB;AACnC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,QAAQ,EAAE,kDAAkD;AAClE,MAAM,MAAM,EAAE,wDAAwD;AACtE,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,oEAAoE;AACrF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0HAA0H;AACzI,MAAM,MAAM,EAAE,oEAAoE;AAClF,MAAM,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClC,MAAM,iBAAiB,EAAE,sFAAsF;AAC/G,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,kDAAkD;AAC/D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}