{"version": 3, "file": "carousel-item2.js", "sources": ["../../../../../../packages/components/carousel/src/carousel-item.vue"], "sourcesContent": ["<template>\n  <div\n    v-show=\"ready\"\n    ref=\"carouselItemRef\"\n    :class=\"itemKls\"\n    :style=\"itemStyle\"\n    @click=\"handleItemClick\"\n  >\n    <div v-if=\"isCardType\" v-show=\"!active\" :class=\"ns.e('mask')\" />\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { carouselItemProps } from './carousel-item'\nimport { useCarouselItem } from './use-carousel-item'\nimport { CAROUSEL_ITEM_NAME } from './constants'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: CAROUSEL_ITEM_NAME,\n})\n\nconst props = defineProps(carouselItemProps)\nconst ns = useNamespace('carousel')\n\n// inject\nconst {\n  carouselItemRef,\n  active,\n  animating,\n  hover,\n  inStage,\n  isVertical,\n  translate,\n  isCardType,\n  scale,\n  ready,\n  handleItemClick,\n} = useCarouselItem(props)\n\nconst itemKls = computed(() => [\n  ns.e('item'),\n  ns.is('active', active.value),\n  ns.is('in-stage', inStage.value),\n  ns.is('hover', hover.value),\n  ns.is('animating', animating.value),\n  {\n    [ns.em('item', 'card')]: isCardType.value,\n    [ns.em('item', 'card-vertical')]: isCardType.value && isVertical.value,\n  },\n])\n\nconst itemStyle = computed<CSSProperties>(() => {\n  const translateType = `translate${unref(isVertical) ? 'Y' : 'X'}`\n  const _translate = `${translateType}(${unref(translate)}px)`\n  const _scale = `scale(${unref(scale)})`\n  const transform = [_translate, _scale].join(' ')\n\n  return {\n    transform,\n  }\n})\n</script>\n"], "names": ["CAROUSEL_ITEM_NAME", "useNamespace", "useCarouselItem", "computed", "unref"], "mappings": ";;;;;;;;;;;uCAsBc,CAAA;AAAA,EACZ,IAAM,EAAAA,4BAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAKC,mBAAa,UAAU,CAAA,CAAA;AAGlC,IAAM,MAAA;AAAA,MACJ,eAAA;AAAA,MACA,MAAA;AAAA,MACA,SAAA;AAAA,MACA,KAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,eAAA;AAAA,KACF,GAAIC,gCAAgB,KAAK,CAAA,CAAA;AAEzB,IAAM,MAAA,OAAA,GAAUC,aAAS,MAAM;AAAA,MAC7B,EAAA,CAAG,EAAE,MAAM,CAAA;AAAA,MACX,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,MAAA,CAAO,KAAK,CAAA;AAAA,MAC5B,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,MAC/B,EAAG,CAAA,EAAA,CAAG,OAAS,EAAA,KAAA,CAAM,KAAK,CAAA;AAAA,MAC1B,EAAG,CAAA,EAAA,CAAG,WAAa,EAAA,SAAA,CAAU,KAAK,CAAA;AAAA,MAClC;AAAA,QACE,CAAC,EAAG,CAAA,EAAA,CAAG,QAAQ,MAAM,CAAC,GAAG,UAAW,CAAA,KAAA;AAAA,QACpC,CAAC,GAAG,EAAG,CAAA,MAAA,EAAQ,eAAe,CAAC,GAAG,UAAW,CAAA,KAAA,IAAS,UAAW,CAAA,KAAA;AAAA,OACnE;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYA,aAAwB,MAAM;AAC9C,MAAA,MAAM,gBAAgB,CAAY,SAAA,EAAAC,SAAA,CAAM,UAAU,CAAA,GAAI,MAAM,GAAG,CAAA,CAAA,CAAA;AAC/D,MAAA,MAAM,aAAa,CAAG,EAAA,aAAa,CAAI,CAAA,EAAAA,SAAA,CAAM,SAAS,CAAC,CAAA,GAAA,CAAA,CAAA;AACvD,MAAA,MAAM,MAAS,GAAA,CAAA,MAAA,EAASA,SAAM,CAAA,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA;AACpC,MAAA,MAAM,YAAY,CAAC,UAAA,EAAY,MAAM,CAAA,CAAE,KAAK,GAAG,CAAA,CAAA;AAE/C,MAAO,OAAA;AAAA,QACL,SAAA;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;"}