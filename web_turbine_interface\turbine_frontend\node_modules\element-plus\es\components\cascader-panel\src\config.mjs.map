{"version": 3, "file": "config.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/config.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { NOOP, buildProps, definePropType } from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { PropType } from 'vue'\nimport type {\n  CascaderConfig,\n  CascaderNodePathValue,\n  CascaderOption,\n  CascaderProps,\n  CascaderValue,\n  RenderLabel,\n} from './node'\n\nexport const CommonProps = buildProps({\n  /**\n   * @description specify which key of node object is used as the node's value\n   */\n  modelValue: {\n    type: definePropType<CascaderValue>([Number, String, Array]),\n  },\n  /**\n   * @description data of the options, the key of `value` and `label` can be customize by `CascaderProps`.\n   */\n  options: {\n    type: definePropType<CascaderOption[]>(Array),\n    default: () => [] as CascaderOption[],\n  },\n  /**\n   * @description configuration options, see the following `CascaderProps` table.\n   */\n  props: {\n    type: definePropType<CascaderProps>(Object),\n    default: () => ({} as CascaderProps),\n  },\n} as const)\n\nexport const DefaultProps: CascaderConfig = {\n  /**\n   * @description trigger mode of expanding options\n   */\n  expandTrigger: 'click',\n  /**\n   * @description whether multiple selection is enabled\n   */\n  multiple: false,\n  /**\n   * @description whether checked state of a node not affects its parent and child nodes\n   */\n  checkStrictly: false, // whether all nodes can be selected\n  /**\n   * @description when checked nodes change, whether to emit an array of node's path, if false, only emit the value of node.\n   */\n  emitPath: true, // wether to emit an array of all levels value in which node is located\n  /**\n   * @description whether to dynamic load child nodes, use with `lazyload` attribute\n   */\n  lazy: false,\n  /**\n   * @description method for loading child nodes data, only works when `lazy` is true\n   */\n  lazyLoad: NOOP,\n  /**\n   * @description specify which key of node object is used as the node's value\n   */\n  value: 'value',\n  /**\n   * @description specify which key of node object is used as the node's label\n   */\n  label: 'label',\n  /**\n   * @description specify which key of node object is used as the node's children\n   */\n  children: 'children',\n  /**\n   * @description specify which key of node object is used as the node's leaf\n   */\n  leaf: 'leaf',\n  /**\n   * @description specify which key of node object is used as the node's disabled\n   */\n  disabled: 'disabled',\n  /**\n   * @description hover threshold of expanding options\n   */\n  hoverThreshold: 500,\n}\n\nexport const cascaderPanelProps = buildProps({\n  ...CommonProps,\n  border: {\n    type: Boolean,\n    default: true,\n  },\n  renderLabel: {\n    type: Function as PropType<RenderLabel>,\n  },\n})\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst emitChangeFn = (value: CascaderValue | undefined) => true\n\nexport const cascaderPanelEmits = {\n  [UPDATE_MODEL_EVENT]: emitChangeFn,\n  [CHANGE_EVENT]: emitChangeFn,\n  close: () => true,\n  'expand-change': (value: CascaderNodePathValue) => value,\n}\n\nexport const useCascaderConfig = (props: { props: CascaderProps }) => {\n  return computed(() => ({\n    ...DefaultProps,\n    ...props.props,\n  }))\n}\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,QAAQ,EAAE,KAAK;AACjB,EAAE,aAAa,EAAE,KAAK;AACtB,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,cAAc,EAAE,GAAG;AACrB,EAAE;AACU,MAAC,kBAAkB,GAAG,UAAU,CAAC;AAC7C,EAAE,GAAG,WAAW;AAChB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,CAAC,EAAE;AACH,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC;AACzB,MAAC,kBAAkB,GAAG;AAClC,EAAE,CAAC,kBAAkB,GAAG,YAAY;AACpC,EAAE,CAAC,YAAY,GAAG,YAAY;AAC9B,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,eAAe,EAAE,CAAC,KAAK,KAAK,KAAK;AACnC,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,KAAK,KAAK;AAC5C,EAAE,OAAO,QAAQ,CAAC,OAAO;AACzB,IAAI,GAAG,YAAY;AACnB,IAAI,GAAG,KAAK,CAAC,KAAK;AAClB,GAAG,CAAC,CAAC,CAAC;AACN;;;;"}