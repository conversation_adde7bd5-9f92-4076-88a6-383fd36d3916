{"version": 3, "file": "use-move.mjs", "sources": ["../../../../../../../packages/components/transfer/src/composables/use-move.ts"], "sourcesContent": ["import { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { usePropsAlias } from './use-props-alias'\n\nimport type { SetupContext } from 'vue'\nimport type {\n  TransferCheckedState,\n  TransferDataItem,\n  TransferDirection,\n  TransferEmits,\n  TransferKey,\n  TransferProps,\n} from '../transfer'\n\nexport const useMove = (\n  props: TransferProps,\n  checkedState: TransferCheckedState,\n  emit: SetupContext<TransferEmits>['emit']\n) => {\n  const propsAlias = usePropsAlias(props)\n\n  const _emit = (\n    value: TransferKey[],\n    direction: TransferDirection,\n    movedKeys: TransferKey[]\n  ) => {\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value, direction, movedKeys)\n  }\n\n  const addToLeft = () => {\n    const currentValue = props.modelValue.slice()\n\n    checkedState.rightChecked.forEach((item) => {\n      const index = currentValue.indexOf(item)\n      if (index > -1) {\n        currentValue.splice(index, 1)\n      }\n    })\n    _emit(currentValue, 'left', checkedState.rightChecked)\n  }\n\n  const addToRight = () => {\n    let currentValue = props.modelValue.slice()\n\n    const itemsToBeMoved = props.data\n      .filter((item: TransferDataItem) => {\n        const itemKey = item[propsAlias.value.key]\n        return (\n          checkedState.leftChecked.includes(itemKey) &&\n          !props.modelValue.includes(itemKey)\n        )\n      })\n      .map((item) => item[propsAlias.value.key])\n\n    currentValue =\n      props.targetOrder === 'unshift'\n        ? itemsToBeMoved.concat(currentValue)\n        : currentValue.concat(itemsToBeMoved)\n\n    if (props.targetOrder === 'original') {\n      currentValue = props.data\n        .filter((item) => currentValue.includes(item[propsAlias.value.key]))\n        .map((item) => item[propsAlias.value.key])\n    }\n\n    _emit(currentValue, 'right', checkedState.leftChecked)\n  }\n\n  return {\n    addToLeft,\n    addToRight,\n  }\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,OAAO,GAAG,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,KAAK;AACtD,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1C,EAAE,MAAM,KAAK,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,KAAK;AACjD,IAAI,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAClD,IAAI,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAChD,MAAM,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/C,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACtC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAChD,IAAI,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK;AACvD,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjD,MAAM,OAAO,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC/F,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,IAAI,YAAY,GAAG,KAAK,CAAC,WAAW,KAAK,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC/H,IAAI,IAAI,KAAK,CAAC,WAAW,KAAK,UAAU,EAAE;AAC1C,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9I,KAAK;AACL,IAAI,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}