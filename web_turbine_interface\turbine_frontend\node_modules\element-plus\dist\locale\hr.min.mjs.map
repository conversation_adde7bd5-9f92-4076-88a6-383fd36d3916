{"version": 3, "file": "hr.min.mjs", "sources": ["../../../../packages/locale/lang/hr.ts"], "sourcesContent": ["export default {\n  name: 'hr',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Odaberi datum',\n      selectTime: 'Odaberi vrijeme',\n      startDate: 'Datum početka',\n      startTime: 'Vri<PERSON>me početka',\n      endDate: 'Datum završetka',\n      endTime: 'Vrijeme završetka',\n      prevYear: 'Preth<PERSON>na godina',\n      nextYear: 'Sljedeća godina',\n      prevMonth: 'Prethodni mjesec',\n      nextMonth: 'Slje<PERSON>ći mjesec',\n      year: '',\n      month1: 'Siječanj',\n      month2: 'Veljača',\n      month3: 'O<PERSON><PERSON><PERSON>',\n      month4: 'Travanj',\n      month5: 'Svibanj',\n      month6: '<PERSON>panj',\n      month7: 'Srpanj',\n      month8: '<PERSON><PERSON><PERSON>',\n      month9: 'Rujan',\n      month10: 'Listopad',\n      month11: '<PERSON><PERSON><PERSON>',\n      month12: 'Prosinac',\n      week: 'tjedan',\n      weeks: {\n        sun: 'Ned',\n        mon: 'Pon',\n        tue: 'Uto',\n        wed: 'Sri',\n        thu: 'Čet',\n        fri: 'Pet',\n        sat: 'Sub',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Učitavanje',\n      noMatch: 'Nema pronađenih podataka',\n      noData: 'Nema podataka',\n      placeholder: 'Izaberi',\n    },\n    mention: {\n      loading: 'Učitavanje',\n    },\n    cascader: {\n      noMatch: 'Nema pronađenih podataka',\n      loading: 'Učitavanje',\n      placeholder: 'Izaberi',\n      noData: 'Nema podataka',\n    },\n    pagination: {\n      goto: 'Idi na',\n      pagesize: '/stranica',\n      total: 'Ukupno {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Poruka',\n      confirm: 'OK',\n      cancel: 'Otkaži',\n      error: 'Pogrešan unos',\n    },\n    upload: {\n      deleteTip: 'pritisnite izbriši za brisanje',\n      delete: 'Izbriši',\n      preview: 'Pregled',\n      continue: 'Nastavak',\n    },\n    table: {\n      emptyText: 'Nema podataka',\n      confirmFilter: 'Potvrdi',\n      resetFilter: 'Resetiraj',\n      clearFilter: 'Sve',\n      sumText: 'Suma',\n    },\n    tree: {\n      emptyText: 'Nema podataka',\n    },\n    transfer: {\n      noMatch: 'Nema pronađenih podataka',\n      noData: 'Nema podataka',\n      titles: ['Lista 1', 'Lista 2'], // to be translated\n      filterPlaceholder: 'Unesite ključnu riječ', // to be translated\n      noCheckedFormat: '{total} stavki', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,sBAAsB,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,wBAAwB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,iCAAiC,CAAC,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}