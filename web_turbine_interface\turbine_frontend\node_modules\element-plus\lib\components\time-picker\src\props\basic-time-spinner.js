'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var shared = require('./shared.js');
var runtime = require('../../../../utils/vue/props/runtime.js');

const basicTimeSpinnerProps = runtime.buildProps({
  role: {
    type: String,
    required: true
  },
  spinnerDate: {
    type: runtime.definePropType(Object),
    required: true
  },
  showSeconds: {
    type: Boolean,
    default: true
  },
  arrowControl: Boolean,
  amPmMode: {
    type: runtime.definePropType(String),
    default: ""
  },
  ...shared.disabledTimeListsProps
});

exports.basicTimeSpinnerProps = basicTimeSpinnerProps;
//# sourceMappingURL=basic-time-spinner.js.map
