{"version": 3, "file": "upload-content2.js", "sources": ["../../../../../../packages/components/upload/src/upload-content.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(listType),\n      ns.is('drag', drag),\n      ns.is('disabled', disabled),\n    ]\"\n    :tabindex=\"disabled ? '-1' : '0'\"\n    @click=\"handleClick\"\n    @keydown.self.enter.space=\"handleKeydown\"\n  >\n    <template v-if=\"drag\">\n      <upload-dragger :disabled=\"disabled\" @file=\"uploadFiles\">\n        <slot />\n      </upload-dragger>\n    </template>\n    <template v-else>\n      <slot />\n    </template>\n    <input\n      ref=\"inputRef\"\n      :class=\"ns.e('input')\"\n      :name=\"name\"\n      :disabled=\"disabled\"\n      :multiple=\"multiple\"\n      :accept=\"accept\"\n      type=\"file\"\n      @change=\"handleChange\"\n      @click.stop\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { shallowRef } from 'vue'\nimport { cloneDeep, isEqual } from 'lodash-unified'\nimport { entriesOf, isFunction, isPlainObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport UploadDragger from './upload-dragger.vue'\nimport { uploadContentProps } from './upload-content'\nimport { genFileId } from './upload'\n\nimport type { UploadContentProps } from './upload-content'\nimport type {\n  UploadFile,\n  UploadHooks,\n  UploadRawFile,\n  UploadRequestOptions,\n} from './upload'\n\ndefineOptions({\n  name: 'ElUploadContent',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(uploadContentProps)\nconst ns = useNamespace('upload')\nconst disabled = useFormDisabled()\n\nconst requests = shallowRef<Record<string, XMLHttpRequest | Promise<unknown>>>(\n  {}\n)\nconst inputRef = shallowRef<HTMLInputElement>()\n\nconst uploadFiles = (files: File[]) => {\n  if (files.length === 0) return\n\n  const { autoUpload, limit, fileList, multiple, onStart, onExceed } = props\n\n  if (limit && fileList.length + files.length > limit) {\n    onExceed(files, fileList)\n    return\n  }\n\n  if (!multiple) {\n    files = files.slice(0, 1)\n  }\n\n  for (const file of files) {\n    const rawFile = file as UploadRawFile\n    rawFile.uid = genFileId()\n    onStart(rawFile)\n    if (autoUpload) upload(rawFile)\n  }\n}\n\nconst upload = async (rawFile: UploadRawFile): Promise<void> => {\n  inputRef.value!.value = ''\n\n  if (!props.beforeUpload) {\n    return doUpload(rawFile)\n  }\n\n  let hookResult: Exclude<ReturnType<UploadHooks['beforeUpload']>, Promise<any>>\n  let beforeData: UploadContentProps['data'] = {}\n\n  try {\n    // origin data: Handle data changes after synchronization tasks are executed\n    const originData = props.data\n    const beforeUploadPromise = props.beforeUpload(rawFile)\n    beforeData = isPlainObject(props.data) ? cloneDeep(props.data) : props.data\n    hookResult = await beforeUploadPromise\n    if (isPlainObject(props.data) && isEqual(originData, beforeData)) {\n      beforeData = cloneDeep(props.data)\n    }\n  } catch {\n    hookResult = false\n  }\n\n  if (hookResult === false) {\n    props.onRemove(rawFile)\n    return\n  }\n\n  let file: File = rawFile\n  if (hookResult instanceof Blob) {\n    if (hookResult instanceof File) {\n      file = hookResult\n    } else {\n      file = new File([hookResult], rawFile.name, {\n        type: rawFile.type,\n      })\n    }\n  }\n\n  doUpload(\n    Object.assign(file, {\n      uid: rawFile.uid,\n    }),\n    beforeData\n  )\n}\n\nconst resolveData = async (\n  data: UploadContentProps['data'],\n  rawFile: UploadRawFile\n): Promise<Record<string, any>> => {\n  if (isFunction(data)) {\n    return data(rawFile)\n  }\n\n  return data\n}\n\nconst doUpload = async (\n  rawFile: UploadRawFile,\n  beforeData?: UploadContentProps['data']\n) => {\n  const {\n    headers,\n    data,\n    method,\n    withCredentials,\n    name: filename,\n    action,\n    onProgress,\n    onSuccess,\n    onError,\n    httpRequest,\n  } = props\n\n  try {\n    beforeData = await resolveData(beforeData ?? data, rawFile)\n  } catch {\n    props.onRemove(rawFile)\n    return\n  }\n\n  const { uid } = rawFile\n  const options: UploadRequestOptions = {\n    headers: headers || {},\n    withCredentials,\n    file: rawFile,\n    data: beforeData,\n    method,\n    filename,\n    action,\n    onProgress: (evt) => {\n      onProgress(evt, rawFile)\n    },\n    onSuccess: (res) => {\n      onSuccess(res, rawFile)\n      delete requests.value[uid]\n    },\n    onError: (err) => {\n      onError(err, rawFile)\n      delete requests.value[uid]\n    },\n  }\n  const request = httpRequest(options)\n  requests.value[uid] = request\n  if (request instanceof Promise) {\n    request.then(options.onSuccess, options.onError)\n  }\n}\n\nconst handleChange = (e: Event) => {\n  const files = (e.target as HTMLInputElement).files\n  if (!files) return\n  uploadFiles(Array.from(files))\n}\n\nconst handleClick = () => {\n  if (!disabled.value) {\n    inputRef.value!.value = ''\n    inputRef.value!.click()\n  }\n}\n\nconst handleKeydown = () => {\n  handleClick()\n}\n\nconst abort = (file?: UploadFile) => {\n  const _reqs = entriesOf(requests.value).filter(\n    file ? ([uid]) => String(file.uid) === uid : () => true\n  )\n  _reqs.forEach(([uid, req]) => {\n    if (req instanceof XMLHttpRequest) req.abort()\n    delete requests.value[uid]\n  })\n}\n\ndefineExpose({\n  abort,\n  upload,\n})\n</script>\n"], "names": ["useNamespace", "useFormDisabled", "shallowRef", "genFileId", "upload", "isPlainObject", "cloneDeep", "isEqual", "isFunction", "entriesOf", "_openBlock", "_createElementBlock", "_normalizeClass"], "mappings": ";;;;;;;;;;;;;;;uCAoDc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAKA,mBAAa,QAAQ,CAAA,CAAA;AAChC,IAAA,MAAM,WAAWC,kCAAgB,EAAA,CAAA;AAEjC,IAAA,MAAM,QAAW,GAAAC,cAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IAAA,MACd,QAAA,GAAAA,cAAA,EAAA,CAAA;AAAA,IACH,MAAA,WAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAM,YAAwC,KAAA,CAAA;AAE9C,QAAM,OAAA;AACJ,MAAI,MAAA,EAAA,iBAAoB,EAAA,QAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,GAAA,KAAA,CAAA;AAExB,MAAA,IAAA,KAAoB,IAAA,QAAA,CAAA,eAAiB,MAAU,GAAA,KAAA,EAAA;AAE/C,QAAA,QAAa,CAAA,KAAA,EAAA,QAAkB,CAAA,CAAA;AAC7B,QAAA,OAAA;AACA,OAAA;AAAA,MACF,IAAA,CAAA,QAAA,EAAA;AAEA,QAAA,KAAe,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACb,OAAQ;AAAgB,MAC1B,KAAA,MAAA,IAAA,IAAA,KAAA,EAAA;AAEA,QAAA,MAAA,cAA0B,CAAA;AACxB,QAAA,OAAgB,CAAA,GAAA,GAAAC,gBAAA,EAAA,CAAA;AAChB,QAAA,OAAA,CAAQ,OAAgB,CAAA,CAAA;AACxB,QAAA,IAAA,UAAe;AACf,UAAIC,QAAA,CAAA;AAA0B,OAChC;AAAA,KACF,CAAA;AAEA,IAAM,MAAAA,QAAA,GAAS,OAAO,OAA0C,KAAA;AAC9D,MAAA,QAAA,CAAS,MAAO,KAAQ,GAAA,EAAA,CAAA;AAExB,MAAI,IAAA,CAAC,MAAM,YAAc,EAAA;AACvB,QAAA,OAAO,SAAS,OAAO,CAAA,CAAA;AAAA,OACzB;AAEA,MAAI,IAAA,UAAA,CAAA;AACJ,MAAA,IAAI,aAAyC,EAAC,CAAA;AAE9C,MAAI,IAAA;AAEF,QAAA,MAAM,aAAa,KAAM,CAAA,IAAA,CAAA;AACzB,QAAM,MAAA,mBAAA,GAAsB,KAAM,CAAA,YAAA,CAAa,OAAO,CAAA,CAAA;AACtD,QAAa,UAAA,GAAAC,oBAAA,CAAc,MAAM,IAAI,CAAA,GAAIC,wBAAU,KAAM,CAAA,IAAI,IAAI,KAAM,CAAA,IAAA,CAAA;AACvE,QAAA,UAAA,GAAa,MAAM,mBAAA,CAAA;AACnB,QAAA,IAAID,qBAAc,KAAM,CAAA,IAAI,KAAKE,qBAAQ,CAAA,UAAA,EAAY,UAAU,CAAG,EAAA;AAChE,UAAa,UAAA,GAAAD,uBAAA,CAAU,MAAM,IAAI,CAAA,CAAA;AAAA,SACnC;AAAA,OACM,CAAA,OAAA,CAAA,EAAA;AACN,QAAa,UAAA,GAAA,KAAA,CAAA;AAAA,OACf;AAEA,MAAA,IAAI,eAAe,KAAO,EAAA;AACxB,QAAA,KAAA,CAAM,SAAS,OAAO,CAAA,CAAA;AACtB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,IAAa,GAAA,OAAA,CAAA;AACjB,MAAA,IAAI,sBAAsB,IAAM,EAAA;AAC9B,QAAA,IAAI,sBAAsB,IAAM,EAAA;AAC9B,UAAO,IAAA,GAAA,UAAA,CAAA;AAAA,SACF,MAAA;AACL,UAAA,IAAA,GAAO,IAAI,IAAK,CAAA,CAAC,UAAU,CAAA,EAAG,QAAQ,IAAM,EAAA;AAAA,YAC1C,MAAM,OAAQ,CAAA,IAAA;AAAA,WACf,CAAA,CAAA;AAAA,SACH;AAAA,OACF;AAEA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,EAAA;AAAA,QACE,GAAA,EAAA,WAAoB;AAAA,OAAA,CAAA,EAClB,UAAa,CAAA,CAAA;AAAA,KAAA,CAAA;AACd,IACD,MAAA,WAAA,GAAA,OAAA,IAAA,EAAA,OAAA,KAAA;AAAA,MACF,IAAAE,iBAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QACF,OAAA,IAAA,CAAA,OAAA,CAAA,CAAA;AAEA,OAAM;AAIJ,MAAI,OAAA,IAAA,CAAA;AACF,KAAA,CAAA;AAAmB,IACrB,MAAA,QAAA,GAAA,OAAA,OAAA,EAAA,UAAA,KAAA;AAEA,MAAO,MAAA;AAAA,QACT,OAAA;AAEA,QAAM,IAAA;AAIJ,QAAM,MAAA;AAAA,QACJ,eAAA;AAAA,QACA,IAAA,EAAA,QAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA;AAAA,QACA,SAAM;AAAA,QACN,OAAA;AAAA,QACA,WAAA;AAAA,OACA,GAAA,KAAA,CAAA;AAAA,MACA,IAAA;AAAA,QACA,UAAA,GAAA,MAAA,WAAA,CAAA,UAAA,IAAA,IAAA,GAAA,UAAA,GAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OACE,CAAA,OAAA,CAAA,EAAA;AAEJ,QAAI,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AACF,QAAA,OAAA;AAA0D,OACpD;AACN,MAAA,MAAA,EAAM,eAAgB,CAAA;AACtB,MAAA,MAAA,OAAA,GAAA;AAAA,QACF,OAAA,EAAA,OAAA,IAAA,EAAA;AAEA,QAAM,eAAU;AAChB,QAAA,IAAM,EAAgC,OAAA;AAAA,QACpC,IAAA,EAAA;AAAqB,QACrB,MAAA;AAAA,QACA,QAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAA,EAAA,CAAA,GAAA,KAAA;AAAA,UACA,UAAA,CAAA,GAAA,EAAA,OAAA,CAAA,CAAA;AAAA,SACA;AAAA,QACA,SAAA,EAAA,CAAY,GAAS,KAAA;AACnB,UAAA,SAAA,CAAA,YAAuB,CAAA,CAAA;AAAA,UACzB,OAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,SACA;AACE,QAAA,OAAA,EAAA,CAAA,QAAsB;AACtB,UAAO,OAAA,CAAA,GAAA,EAAA,SAAe;AAAG,UAC3B,OAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,SACA;AACE,OAAA,CAAA;AACA,MAAO,MAAA,OAAA,GAAA,WAAkB,CAAA,OAAA,CAAA,CAAA;AAAA,MAC3B,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,OAAA,CAAA;AAAA,MACF,IAAA,OAAA,YAAA,OAAA,EAAA;AACA,QAAM,OAAA,CAAA,IAAA,CAAA,iBAA6B,EAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACnC,OAAS;AACT,KAAA,CAAA;AACE,IAAA,MAAA,YAAa,GAAA,CAAA,CAAA,KAAmB;AAAe,MACjD,MAAA,KAAA,GAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA;AAAA,MACF,IAAA,CAAA,KAAA;AAEA,QAAM,OAAA;AACJ,MAAM,WAAA,CAAA,KAAuC,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAC7C,KAAA,CAAA;AACA,IAAY,MAAA,WAAA,GAAM,MAAK;AAAM,MAC/B,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA;AAEA,QAAA,oBAA0B,GAAA,EAAA,CAAA;AACxB,QAAI,cAAiB,CAAA,KAAA,EAAA,CAAA;AACnB,OAAA;AACA,KAAA,CAAA;AAAsB,IACxB,MAAA,aAAA,GAAA,MAAA;AAAA,MACF,WAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAY,MAAA,KAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACd,MAAA,KAAA,GAAAC,iBAAA,CAAA,QAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,CAAA,KAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA,GAAA,GAAA,MAAA,IAAA,CAAA,CAAA;AAEA,MAAM,KAAA,CAAA,OAAS,CAAsB,CAAA,CAAA,GAAA,EAAA,GAAA,CAAA,KAAA;AACnC,QAAA,IAAM,GAAQ,YAAA,cAAmB;AAAO,UACtC,GAAA,CAAA,KAAY,EAAA,CAAA;AAAuC,QACrD,OAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACE,KAAI,CAAA;AACJ,IAAO,MAAA,CAAA;AAAkB,MAC3B,KAAC;AAAA,cACHL,QAAA;AAEA,KAAa,CAAA,CAAA;AAAA,IACX,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACA,OAAAM,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACD,KAAA,EAAAC,kBAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}