{"version": 3, "file": "affix2.js", "sources": ["../../../../../../packages/components/affix/src/affix.vue"], "sourcesContent": ["<template>\n  <div ref=\"root\" :class=\"ns.b()\" :style=\"rootStyle\">\n    <div :class=\"{ [ns.m('fixed')]: fixed }\" :style=\"affixStyle\">\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  useElementBounding,\n  useEventListener,\n  useWindowSize,\n} from '@vueuse/core'\nimport { addUnit, getScrollContainer, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { affixEmits, affixProps } from './affix'\n\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElAffix'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(affixProps)\nconst emit = defineEmits(affixEmits)\n\nconst ns = useNamespace('affix')\n\nconst target = shallowRef<HTMLElement>()\nconst root = shallowRef<HTMLDivElement>()\nconst scrollContainer = shallowRef<HTMLElement | Window>()\nconst { height: windowHeight } = useWindowSize()\nconst {\n  height: rootHeight,\n  width: rootWidth,\n  top: rootTop,\n  bottom: rootBottom,\n  update: updateRoot,\n} = useElementBounding(root, { windowScroll: false })\nconst targetRect = useElementBounding(target)\n\nconst fixed = ref(false)\nconst scrollTop = ref(0)\nconst transform = ref(0)\n\nconst rootStyle = computed<CSSProperties>(() => {\n  return {\n    height: fixed.value ? `${rootHeight.value}px` : '',\n    width: fixed.value ? `${rootWidth.value}px` : '',\n  }\n})\n\nconst affixStyle = computed<CSSProperties>(() => {\n  if (!fixed.value) return {}\n\n  const offset = props.offset ? addUnit(props.offset) : 0\n  return {\n    height: `${rootHeight.value}px`,\n    width: `${rootWidth.value}px`,\n    top: props.position === 'top' ? offset : '',\n    bottom: props.position === 'bottom' ? offset : '',\n    transform: transform.value ? `translateY(${transform.value}px)` : '',\n    zIndex: props.zIndex,\n  }\n})\n\nconst update = () => {\n  if (!scrollContainer.value) return\n\n  scrollTop.value =\n    scrollContainer.value instanceof Window\n      ? document.documentElement.scrollTop\n      : scrollContainer.value.scrollTop || 0\n\n  const { position, target, offset } = props\n  const rootHeightOffset = offset + rootHeight.value\n\n  if (position === 'top') {\n    if (target) {\n      const difference = targetRect.bottom.value - rootHeightOffset\n      fixed.value = offset > rootTop.value && targetRect.bottom.value > 0\n      transform.value = difference < 0 ? difference : 0\n    } else {\n      fixed.value = offset > rootTop.value\n    }\n  } else if (target) {\n    const difference =\n      windowHeight.value - targetRect.top.value - rootHeightOffset\n    fixed.value =\n      windowHeight.value - offset < rootBottom.value &&\n      windowHeight.value > targetRect.top.value\n    transform.value = difference < 0 ? -difference : 0\n  } else {\n    fixed.value = windowHeight.value - offset < rootBottom.value\n  }\n}\n\nconst updateRootRect = async () => {\n  if (!fixed.value) {\n    updateRoot()\n    return\n  }\n\n  fixed.value = false\n  await nextTick()\n  updateRoot()\n  fixed.value = true\n}\n\nconst handleScroll = async () => {\n  updateRoot()\n  await nextTick()\n  emit('scroll', {\n    scrollTop: scrollTop.value,\n    fixed: fixed.value,\n  })\n}\n\nwatch(fixed, (val) => emit(CHANGE_EVENT, val))\n\nonMounted(() => {\n  if (props.target) {\n    target.value =\n      document.querySelector<HTMLElement>(props.target) ?? undefined\n    if (!target.value)\n      throwError(COMPONENT_NAME, `Target does not exist: ${props.target}`)\n  } else {\n    target.value = document.documentElement\n  }\n  scrollContainer.value = getScrollContainer(root.value!, true)\n  updateRoot()\n})\n\nuseEventListener(scrollContainer, 'scroll', handleScroll)\nwatchEffect(update)\n\ndefineExpose({\n  /** @description update affix status */\n  update,\n  /** @description update rootRect info */\n  updateRoot: updateRootRect,\n})\n</script>\n"], "names": ["useNamespace", "shallowRef", "useWindowSize", "useElementBounding", "ref", "computed", "addUnit", "target", "nextTick", "watch", "CHANGE_EVENT", "onMounted", "throwError", "getScrollContainer", "useEventListener", "watchEffect"], "mappings": ";;;;;;;;;;;;;;;uCA+Bc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,OAAO,CAAA,CAAA;AAE/B,IAAA,MAAM,SAASC,cAAwB,EAAA,CAAA;AACvC,IAAA,MAAM,OAAOA,cAA2B,EAAA,CAAA;AACxC,IAAA,MAAM,kBAAkBA,cAAiC,EAAA,CAAA;AACzD,IAAA,MAAM,EAAE,MAAA,EAAQ,YAAa,EAAA,GAAIC,kBAAc,EAAA,CAAA;AAC/C,IAAM,MAAA;AAAA,MACJ,MAAQ,EAAA,UAAA;AAAA,MACR,KAAO,EAAA,SAAA;AAAA,MACP,GAAK,EAAA,OAAA;AAAA,MACL,MAAQ,EAAA,UAAA;AAAA,MACR,MAAQ,EAAA,UAAA;AAAA,QACNC,uBAAmB,CAAA,IAAA,EAAM,EAAE,YAAA,EAAc,OAAO,CAAA,CAAA;AACpD,IAAM,MAAA,UAAA,GAAaA,wBAAmB,MAAM,CAAA,CAAA;AAE5C,IAAM,MAAA,KAAA,GAAQC,QAAI,KAAK,CAAA,CAAA;AACvB,IAAM,MAAA,SAAA,GAAYA,QAAI,CAAC,CAAA,CAAA;AACvB,IAAM,MAAA,SAAA,GAAYA,QAAI,CAAC,CAAA,CAAA;AAEvB,IAAM,MAAA,SAAA,GAAYC,aAAwB,MAAM;AAC9C,MAAO,OAAA;AAAA,QACL,QAAQ,KAAM,CAAA,KAAA,GAAQ,CAAG,EAAA,UAAA,CAAW,KAAK,CAAO,EAAA,CAAA,GAAA,EAAA;AAAA,QAChD,OAAO,KAAM,CAAA,KAAA,GAAQ,CAAG,EAAA,SAAA,CAAU,KAAK,CAAO,EAAA,CAAA,GAAA,EAAA;AAAA,OAChD,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaA,aAAwB,MAAM;AAC/C,MAAA,IAAI,CAAC,KAAA,CAAM,KAAO;AAElB,QAAA;AACA,MAAO,MAAA,MAAA,GAAA,KAAA,CAAA,MAAA,GAAAC,aAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA;AAAA,MACL,OAAA;AAA2B,QAC3B,MAAA,EAAU,CAAA,EAAA,UAAe,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,QACzB,KAAK,EAAA,CAAA,EAAA,SAAmB,CAAA,KAAA,CAAA,EAAA,CAAA;AAAiB,QACzC,GAAQ,EAAA,KAAA,CAAA,QAAmB,KAAA,KAAA,GAAA,MAAW,GAAS,EAAA;AAAA,QAC/C,aAAqB,CAAA,QAAA,KAAA,QAAsB,GAAA,MAAA,GAAA,EAAA;AAAuB,QAClE,SAAc,EAAA,SAAA,CAAA,KAAA,GAAA,CAAA,WAAA,EAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA;AAAA,QAChB,MAAA,EAAA,KAAA,CAAA,MAAA;AAAA,OACD,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAI,MAAA;AAEJ,MAAU,IAAA,CAAA;AAKV,QAAA,OAAQ;AACR,MAAM,SAAA,CAAA,KAAA,GAAA,gBAA4B,KAAW,YAAA,MAAA,GAAA,QAAA,CAAA,eAAA,CAAA,SAAA,GAAA,eAAA,CAAA,KAAA,CAAA,SAAA,IAAA,CAAA,CAAA;AAE7C,MAAA,kBAAwB,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,GAAA,KAAA,CAAA;AACtB,MAAA,MAAIC,gBAAQ,GAAA,MAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AACV,MAAM,IAAA,QAAA,KAAA,KAAA,EAAwB;AAC9B,QAAA,IAAA;AACA,UAAU,MAAA,UAAA,GAAqB,UAAA,CAAA,MAAiB,CAAA,KAAA,GAAA,gBAAA,CAAA;AAAA,UAC3C,KAAA,CAAA,KAAA,GAAA,MAAA,GAAA,OAAA,CAAA,KAAA,IAAA,UAAA,CAAA,MAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AACL,UAAM,SAAA,CAAA,kBAAyB,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,CAAA;AAAA,SACjC,MAAA;AAAA,qBACiB,GAAA,MAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AACjB,SAAA;AAEA,OAAM,MAAA,IAAA;AAGN,QAAA,MAAA,UAAkB,GAAA,YAAa,CAAI,KAAc,GAAA,UAAA,CAAA,GAAA,CAAA,KAAA,GAAA,gBAAA,CAAA;AAAA,QAC5C,KAAA,CAAA,KAAA,GAAA,YAAA,CAAA,KAAA,GAAA,MAAA,GAAA,UAAA,CAAA,KAAA,IAAA,YAAA,CAAA,KAAA,GAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA;AACL,QAAA,SAAc,CAAA,KAAA,GAAA,UAAqB,GAAA,CAAA,GAAA,CAAA,UAAoB,GAAA,CAAA,CAAA;AAAA,OACzD,MAAA;AAAA,QACF,KAAA,CAAA,KAAA,GAAA,YAAA,CAAA,KAAA,GAAA,MAAA,GAAA,UAAA,CAAA,KAAA,CAAA;AAEA,OAAA;AACE,KAAI,CAAA;AACF,IAAW,MAAA,cAAA,GAAA,YAAA;AACX,MAAA,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA;AAAA,QACF,UAAA,EAAA,CAAA;AAEA,QAAA,OAAc;AACd,OAAA;AACA,MAAW,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACX,MAAA,MAAMC,YAAQ,EAAA,CAAA;AAAA,MAChB,UAAA,EAAA,CAAA;AAEA,MAAA,mBAAqB;AACnB,KAAW,CAAA;AACX,IAAA,MAAA,YAAe,GAAA,YAAA;AACf,MAAA,UAAe,EAAA,CAAA;AAAA,MAAA,kBACQ,EAAA,CAAA;AAAA,MAAA,aACR,EAAA;AAAA,QACd,SAAA,EAAA,SAAA,CAAA,KAAA;AAAA,QACH,KAAA,EAAA,KAAA,CAAA,KAAA;AAEA,OAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAAC,SAAA,CAAI,OAAc,CAAA,GAAA,KAAA,IAAA,CAAAC,kBAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AAChB,IAAAC,aAAA,CAAA,MACE;AACF,MAAA,IAAA,EAAI,CAAC;AACH,MAAA,IAAA,KAAA,CAAA,MAA2B,EAAA;AAAwC,QAChE,MAAA,CAAA,KAAA,GAAA,CAAA,EAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,CAAA;AACL,QAAA,IAAA,CAAA,YAAwB;AAAA,UAC1BC,gBAAA,CAAA,cAAA,EAAA,CAAA,uBAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA,MAAA;AACA,QAAW,MAAA,CAAA,KAAA,GAAA,QAAA,CAAA,eAAA,CAAA;AAAA,OACZ;AAED,MAAiB,eAAA,CAAA,KAAA,GAAAC,yBAA2B,CAAY,IAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACxD,MAAA,UAAY,EAAM,CAAA;AAElB,KAAa,CAAA,CAAA;AAAA,IAAAC,qBAAA,CAAA,eAAA,EAAA,QAAA,EAAA,YAAA,CAAA,CAAA;AAAA,IAEXC,eAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAEA,MAAY;AAAA,MACb,UAAA,EAAA,cAAA;;;;;;;;;;;;;;;;;;;;;;;"}