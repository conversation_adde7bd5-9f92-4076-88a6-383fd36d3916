export { default as af } from './locale/lang/af.mjs';
export { default as arEg } from './locale/lang/ar-eg.mjs';
export { default as ar } from './locale/lang/ar.mjs';
export { default as az } from './locale/lang/az.mjs';
export { default as bg } from './locale/lang/bg.mjs';
export { default as bn } from './locale/lang/bn.mjs';
export { default as ca } from './locale/lang/ca.mjs';
export { default as ckb } from './locale/lang/ckb.mjs';
export { default as cs } from './locale/lang/cs.mjs';
export { default as da } from './locale/lang/da.mjs';
export { default as de } from './locale/lang/de.mjs';
export { default as el } from './locale/lang/el.mjs';
export { default as en } from './locale/lang/en.mjs';
export { default as eo } from './locale/lang/eo.mjs';
export { default as es } from './locale/lang/es.mjs';
export { default as et } from './locale/lang/et.mjs';
export { default as eu } from './locale/lang/eu.mjs';
export { default as fa } from './locale/lang/fa.mjs';
export { default as fi } from './locale/lang/fi.mjs';
export { default as fr } from './locale/lang/fr.mjs';
export { default as he } from './locale/lang/he.mjs';
export { default as hi } from './locale/lang/hi.mjs';
export { default as hr } from './locale/lang/hr.mjs';
export { default as hu } from './locale/lang/hu.mjs';
export { default as hyAm } from './locale/lang/hy-am.mjs';
export { default as id } from './locale/lang/id.mjs';
export { default as it } from './locale/lang/it.mjs';
export { default as ja } from './locale/lang/ja.mjs';
export { default as kk } from './locale/lang/kk.mjs';
export { default as km } from './locale/lang/km.mjs';
export { default as ko } from './locale/lang/ko.mjs';
export { default as ku } from './locale/lang/ku.mjs';
export { default as ky } from './locale/lang/ky.mjs';
export { default as lo } from './locale/lang/lo.mjs';
export { default as lt } from './locale/lang/lt.mjs';
export { default as lv } from './locale/lang/lv.mjs';
export { default as mg } from './locale/lang/mg.mjs';
export { default as mn } from './locale/lang/mn.mjs';
export { default as ms } from './locale/lang/ms.mjs';
export { default as my } from './locale/lang/my.mjs';
export { default as nbNo } from './locale/lang/nb-no.mjs';
export { default as nl } from './locale/lang/nl.mjs';
export { default as no } from './locale/lang/no.mjs';
export { default as pa } from './locale/lang/pa.mjs';
export { default as pl } from './locale/lang/pl.mjs';
export { default as ptBr } from './locale/lang/pt-br.mjs';
export { default as pt } from './locale/lang/pt.mjs';
export { default as ro } from './locale/lang/ro.mjs';
export { default as ru } from './locale/lang/ru.mjs';
export { default as sk } from './locale/lang/sk.mjs';
export { default as sl } from './locale/lang/sl.mjs';
export { default as sr } from './locale/lang/sr.mjs';
export { default as sv } from './locale/lang/sv.mjs';
export { default as sw } from './locale/lang/sw.mjs';
export { default as ta } from './locale/lang/ta.mjs';
export { default as te } from './locale/lang/te.mjs';
export { default as th } from './locale/lang/th.mjs';
export { default as tk } from './locale/lang/tk.mjs';
export { default as tr } from './locale/lang/tr.mjs';
export { default as ugCn } from './locale/lang/ug-cn.mjs';
export { default as uk } from './locale/lang/uk.mjs';
export { default as uzUz } from './locale/lang/uz-uz.mjs';
export { default as vi } from './locale/lang/vi.mjs';
export { default as zhCn } from './locale/lang/zh-cn.mjs';
export { default as zhTw } from './locale/lang/zh-tw.mjs';
export { default as zhHk } from './locale/lang/zh-hk.mjs';
export { default as zhMo } from './locale/lang/zh-mo.mjs';
//# sourceMappingURL=locales.mjs.map
