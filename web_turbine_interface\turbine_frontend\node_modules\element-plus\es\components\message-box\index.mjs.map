{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/message-box/index.ts"], "sourcesContent": ["import MessageBox from './src/messageBox'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nconst _MessageBox = MessageBox as SFCWithInstall<typeof MessageBox>\n\n_MessageBox.install = (app: App) => {\n  _MessageBox._context = app._context\n  app.config.globalProperties.$msgbox = _MessageBox\n  app.config.globalProperties.$messageBox = _MessageBox\n  app.config.globalProperties.$alert = _MessageBox.alert\n  app.config.globalProperties.$confirm = _MessageBox.confirm\n  app.config.globalProperties.$prompt = _MessageBox.prompt\n}\n\nexport default _MessageBox\nexport const ElMessageBox = _MessageBox\n\nexport * from './src/message-box.type'\n"], "names": [], "mappings": ";;AACK,MAAC,WAAW,GAAG,WAAW;AAC/B,WAAW,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AAC/B,EAAE,WAAW,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AACtC,EAAE,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,GAAG,WAAW,CAAC;AACpD,EAAE,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,GAAG,WAAW,CAAC;AACxD,EAAE,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC;AACzD,EAAE,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC;AAC7D,EAAE,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC;AAC3D,CAAC,CAAC;AAEU,MAAC,YAAY,GAAG;;;;"}