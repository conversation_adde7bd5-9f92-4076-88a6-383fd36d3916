{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/countdown/src/utils.ts"], "sourcesContent": ["import { isNumber } from '@element-plus/utils'\n\nimport type { Dayjs } from 'dayjs'\n\nconst timeUnits = [\n  ['Y', 1000 * 60 * 60 * 24 * 365], // years\n  ['M', 1000 * 60 * 60 * 24 * 30], // months\n  ['D', 1000 * 60 * 60 * 24], // days\n  ['H', 1000 * 60 * 60], // hours\n  ['m', 1000 * 60], // minutes\n  ['s', 1000], // seconds\n  ['S', 1], // million seconds\n] as const\n\nexport const getTime = (value: number | Dayjs) => {\n  return isNumber(value) ? new Date(value).getTime() : value.valueOf()\n}\n\nexport const formatTime = (timestamp: number, format: string) => {\n  let timeLeft = timestamp\n  const escapeRegex = /\\[([^\\]]*)]/g\n\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    const replaceRegex = new RegExp(`${name}+(?![^\\\\[\\\\]]*\\\\])`, 'g')\n    if (replaceRegex.test(current)) {\n      const value = Math.floor(timeLeft / unit)\n      timeLeft -= value * unit\n      return current.replace(replaceRegex, (match) =>\n        String(value).padStart(match.length, '0')\n      )\n    }\n    return current\n  }, format)\n\n  return replacedText.replace(escapeRegex, '$1')\n}\n"], "names": ["isNumber"], "mappings": ";;;;;;AACA,MAAM,SAAS,GAAG;AAClB,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;AACjC,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAChC,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3B,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACtB,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;AACjB,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AACZ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACV,CAAC,CAAC;AACU,MAAC,OAAO,GAAG,CAAC,KAAK,KAAK;AAClC,EAAE,OAAOA,cAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AACvE,EAAE;AACU,MAAC,UAAU,GAAG,CAAC,SAAS,EAAE,MAAM,KAAK;AACjD,EAAE,IAAI,QAAQ,GAAG,SAAS,CAAC;AAC3B,EAAE,MAAM,WAAW,GAAG,cAAc,CAAC;AACrC,EAAE,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK;AACnE,IAAI,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE,GAAG,CAAC,CAAC;AACtE,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AACpC,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;AAChD,MAAM,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AAC/B,MAAM,OAAO,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AACjG,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG,EAAE,MAAM,CAAC,CAAC;AACb,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AACjD;;;;;"}