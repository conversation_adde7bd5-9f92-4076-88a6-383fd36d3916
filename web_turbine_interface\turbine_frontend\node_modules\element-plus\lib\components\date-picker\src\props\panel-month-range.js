'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var shared = require('./shared.js');
var runtime = require('../../../../utils/vue/props/runtime.js');

const panelMonthRangeProps = runtime.buildProps({
  ...shared.panelRangeSharedProps
});
const panelMonthRangeEmits = [
  "pick",
  "set-picker-option",
  "calendar-change"
];

exports.panelMonthRangeEmits = panelMonthRangeEmits;
exports.panelMonthRangeProps = panelMonthRangeProps;
//# sourceMappingURL=panel-month-range.js.map
