{"version": 3, "file": "use-alpha-slider.js", "sources": ["../../../../../../../packages/components/color-picker/src/composables/use-alpha-slider.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { addUnit, getClientXY } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { draggable } from '../utils/draggable'\n\nimport type { AlphaSliderProps } from '../props/alpha-slider'\n\nexport const useAlphaSlider = (props: AlphaSliderProps) => {\n  const instance = getCurrentInstance()!\n  const { t } = useLocale()\n\n  const thumb = shallowRef<HTMLElement>()\n  const bar = shallowRef<HTMLElement>()\n\n  const alpha = computed(() => props.color.get('alpha'))\n  const alphaLabel = computed(() => t('el.colorpicker.alphaLabel'))\n\n  function handleClick(event: MouseEvent | TouchEvent) {\n    const target = event.target\n\n    if (target !== thumb.value) {\n      handleDrag(event)\n    }\n    thumb.value?.focus()\n  }\n\n  function handleDrag(event: MouseEvent | TouchEvent) {\n    if (!bar.value || !thumb.value) return\n\n    const el = instance.vnode.el as HTMLElement\n    const rect = el.getBoundingClientRect()\n    const { clientX, clientY } = getClientXY(event)\n\n    if (!props.vertical) {\n      let left = clientX - rect.left\n      left = Math.max(thumb.value.offsetWidth / 2, left)\n      left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n\n      props.color.set(\n        'alpha',\n        Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            100\n        )\n      )\n    } else {\n      let top = clientY - rect.top\n      top = Math.max(thumb.value.offsetHeight / 2, top)\n      top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n\n      props.color.set(\n        'alpha',\n        Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            100\n        )\n      )\n    }\n  }\n\n  function handleKeydown(event: KeyboardEvent) {\n    const { code, shiftKey } = event\n    const step = shiftKey ? 10 : 1\n\n    switch (code) {\n      case EVENT_CODE.left:\n      case EVENT_CODE.down:\n        event.preventDefault()\n        event.stopPropagation()\n        incrementPosition(-step)\n        break\n      case EVENT_CODE.right:\n      case EVENT_CODE.up:\n        event.preventDefault()\n        event.stopPropagation()\n        incrementPosition(step)\n        break\n    }\n  }\n\n  function incrementPosition(step: number) {\n    let next = alpha.value + step\n    next = next < 0 ? 0 : next > 100 ? 100 : next\n    props.color.set('alpha', next)\n  }\n\n  return {\n    thumb,\n    bar,\n    alpha,\n    alphaLabel,\n    handleDrag,\n    handleClick,\n    handleKeydown,\n  }\n}\n\nexport const useAlphaSliderDOM = (\n  props: AlphaSliderProps,\n  {\n    bar,\n    thumb,\n    handleDrag,\n  }: Pick<ReturnType<typeof useAlphaSlider>, 'bar' | 'thumb' | 'handleDrag'>\n) => {\n  const instance = getCurrentInstance()!\n\n  const ns = useNamespace('color-alpha-slider')\n  // refs\n\n  const thumbLeft = ref(0)\n  const thumbTop = ref(0)\n  const background = ref<string>()\n\n  function getThumbLeft() {\n    if (!thumb.value) return 0\n\n    if (props.vertical) return 0\n    const el = instance.vnode.el\n    const alpha = props.color.get('alpha')\n\n    if (!el) return 0\n    return Math.round(\n      (alpha * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 100\n    )\n  }\n\n  function getThumbTop() {\n    if (!thumb.value) return 0\n\n    const el = instance.vnode.el\n    if (!props.vertical) return 0\n    const alpha = props.color.get('alpha')\n\n    if (!el) return 0\n    return Math.round(\n      (alpha * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 100\n    )\n  }\n\n  function getBackground() {\n    if (props.color && props.color.value) {\n      const { r, g, b } = props.color.toRgb()\n      return `linear-gradient(to right, rgba(${r}, ${g}, ${b}, 0) 0%, rgba(${r}, ${g}, ${b}, 1) 100%)`\n    }\n    return ''\n  }\n\n  function update() {\n    thumbLeft.value = getThumbLeft()\n    thumbTop.value = getThumbTop()\n    background.value = getBackground()\n  }\n\n  onMounted(() => {\n    if (!bar.value || !thumb.value) return\n\n    const dragConfig = {\n      drag: (event: MouseEvent | TouchEvent) => {\n        handleDrag(event)\n      },\n      end: (event: MouseEvent | TouchEvent) => {\n        handleDrag(event)\n      },\n    }\n\n    draggable(bar.value, dragConfig)\n    draggable(thumb.value, dragConfig)\n    update()\n  })\n\n  watch(\n    () => props.color.get('alpha'),\n    () => update()\n  )\n  watch(\n    () => props.color.value,\n    () => update()\n  )\n\n  const rootKls = computed(() => [ns.b(), ns.is('vertical', props.vertical)])\n  const barKls = computed(() => ns.e('bar'))\n  const thumbKls = computed(() => ns.e('thumb'))\n  const barStyle = computed(() => ({ background: background.value }))\n  const thumbStyle = computed(() => ({\n    left: addUnit(thumbLeft.value),\n    top: addUnit(thumbTop.value),\n  }))\n\n  return { rootKls, barKls, barStyle, thumbKls, thumbStyle, update }\n}\n"], "names": ["getCurrentInstance", "useLocale", "shallowRef", "computed", "getClientXY", "EVENT_CODE", "useNamespace", "ref", "onMounted", "draggable", "watch", "addUnit"], "mappings": ";;;;;;;;;;;;AAYY,MAAC,cAAc,GAAG,CAAC,KAAK,KAAK;AACzC,EAAE,MAAM,QAAQ,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAGC,eAAS,EAAE,CAAC;AAC5B,EAAE,MAAM,KAAK,GAAGC,cAAU,EAAE,CAAC;AAC7B,EAAE,MAAM,GAAG,GAAGA,cAAU,EAAE,CAAC;AAC3B,EAAE,MAAM,KAAK,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACzD,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC;AACpE,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAChC,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,KAAK,EAAE;AAChC,MAAM,UAAU,CAAC,KAAK,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACrD,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK;AAClC,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;AACjC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;AAC5C,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGC,oBAAW,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,IAAI,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AACzD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACtE,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAChI,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;AACnC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACxD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AACtE,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClI,KAAK;AACL,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;AACrC,IAAI,MAAM,IAAI,GAAG,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC;AACnC,IAAI,QAAQ,IAAI;AAChB,MAAM,KAAKC,eAAU,CAAC,IAAI,CAAC;AAC3B,MAAM,KAAKA,eAAU,CAAC,IAAI;AAC1B,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,QAAQ,KAAK,CAAC,eAAe,EAAE,CAAC;AAChC,QAAQ,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC;AACjC,QAAQ,MAAM;AACd,MAAM,KAAKA,eAAU,CAAC,KAAK,CAAC;AAC5B,MAAM,KAAKA,eAAU,CAAC,EAAE;AACxB,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,QAAQ,KAAK,CAAC,eAAe,EAAE,CAAC;AAChC,QAAQ,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAChC,QAAQ,MAAM;AACd,KAAK;AACL,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACnC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AAClC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AAClD,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,GAAG;AACP,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,KAAK,EAAE;AACzC,EAAE,GAAG;AACL,EAAE,KAAK;AACP,EAAE,UAAU;AACZ,CAAC,KAAK;AACN,EAAE,MAAM,QAAQ,GAAGL,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,EAAE,GAAGM,oBAAY,CAAC,oBAAoB,CAAC,CAAC;AAChD,EAAE,MAAM,SAAS,GAAGC,OAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,QAAQ,GAAGA,OAAG,CAAC,CAAC,CAAC,CAAC;AAC1B,EAAE,MAAM,UAAU,GAAGA,OAAG,EAAE,CAAC;AAC3B,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK;AACpB,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,IAAI,KAAK,CAAC,QAAQ;AACtB,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;AACjC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACpF,GAAG;AACH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK;AACpB,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ;AACvB,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACtF,GAAG;AACH,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;AAC1C,MAAM,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAC9C,MAAM,OAAO,CAAC,+BAA+B,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC;AACvG,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,MAAM,GAAG;AACpB,IAAI,SAAS,CAAC,KAAK,GAAG,YAAY,EAAE,CAAC;AACrC,IAAI,QAAQ,CAAC,KAAK,GAAG,WAAW,EAAE,CAAC;AACnC,IAAI,UAAU,CAAC,KAAK,GAAG,aAAa,EAAE,CAAC;AACvC,GAAG;AACH,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK;AAClC,MAAM,OAAO;AACb,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,IAAI,EAAE,CAAC,KAAK,KAAK;AACvB,QAAQ,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,OAAO;AACP,MAAM,GAAG,EAAE,CAAC,KAAK,KAAK;AACtB,QAAQ,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,OAAO;AACP,KAAK,CAAC;AACN,IAAIC,mBAAS,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACrC,IAAIA,mBAAS,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACvC,IAAI,MAAM,EAAE,CAAC;AACb,GAAG,CAAC,CAAC;AACL,EAAEC,SAAK,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,MAAM,MAAM,EAAE,CAAC,CAAC;AACxD,EAAEA,SAAK,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,MAAM,EAAE,CAAC,CAAC;AACjD,EAAE,MAAM,OAAO,GAAGP,YAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9E,EAAE,MAAM,MAAM,GAAGA,YAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACjD,EAAE,MAAM,QAAQ,GAAGA,YAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACtE,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,OAAO;AACrC,IAAI,IAAI,EAAEQ,aAAO,CAAC,SAAS,CAAC,KAAK,CAAC;AAClC,IAAI,GAAG,EAAEA,aAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;AAChC,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;AACrE;;;;;"}