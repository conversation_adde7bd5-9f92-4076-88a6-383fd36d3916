'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var row$1 = require('./src/row.js');
var row = require('./src/row2.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElRow = install.withInstall(row$1["default"]);

exports.RowAlign = row.RowAlign;
exports.RowJustify = row.RowJustify;
exports.rowProps = row.rowProps;
exports.rowContextKey = constants.rowContextKey;
exports.ElRow = ElRow;
exports["default"] = ElRow;
//# sourceMappingURL=index.js.map
