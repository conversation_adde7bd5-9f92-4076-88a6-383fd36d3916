{"version": 3, "file": "tab-bar2.js", "sources": ["../../../../../../packages/components/tabs/src/tab-bar.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"barRef\"\n    :class=\"[ns.e('active-bar'), ns.is(rootTabs!.props.tabPosition)]\"\n    :style=\"barStyle\"\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, nextTick, onBeforeUnmount, ref, watch } from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { capitalize, isUndefined, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabBarProps } from './tab-bar'\n\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElTabBar'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabBarProps)\n\nconst rootTabs = inject(tabsRootContextKey)\nif (!rootTabs) throwError(COMPONENT_NAME, '<el-tabs><el-tab-bar /></el-tabs>')\n\nconst ns = useNamespace('tabs')\n\nconst barRef = ref<HTMLDivElement>()\nconst barStyle = ref<CSSProperties>()\n\nconst getBarStyle = (): CSSProperties => {\n  let offset = 0\n  let tabSize = 0\n\n  const sizeName = ['top', 'bottom'].includes(rootTabs.props.tabPosition)\n    ? 'width'\n    : 'height'\n  const sizeDir = sizeName === 'width' ? 'x' : 'y'\n  const position = sizeDir === 'x' ? 'left' : 'top'\n\n  props.tabs.every((tab) => {\n    if (isUndefined(tab.paneName)) return false\n    const $el = props.tabRefs[tab.paneName]\n    if (!$el) return false\n\n    if (!tab.active) {\n      return true\n    }\n\n    offset = $el[`offset${capitalize(position)}`]\n    tabSize = $el[`client${capitalize(sizeName)}`]\n\n    const tabStyles = window.getComputedStyle($el)\n\n    if (sizeName === 'width') {\n      tabSize -=\n        Number.parseFloat(tabStyles.paddingLeft) +\n        Number.parseFloat(tabStyles.paddingRight)\n      offset += Number.parseFloat(tabStyles.paddingLeft)\n    }\n    return false\n  })\n\n  return {\n    [sizeName]: `${tabSize}px`,\n    transform: `translate${capitalize(sizeDir)}(${offset}px)`,\n  }\n}\n\nconst update = () => (barStyle.value = getBarStyle())\n\nconst saveObserver = [] as ReturnType<typeof useResizeObserver>[]\nconst observerTabs = () => {\n  saveObserver.forEach((observer) => observer.stop())\n  saveObserver.length = 0\n\n  Object.values(props.tabRefs).forEach((tab) => {\n    saveObserver.push(useResizeObserver(tab, update))\n  })\n}\n\nwatch(\n  () => props.tabs,\n  async () => {\n    await nextTick()\n    update()\n\n    observerTabs()\n  },\n  { immediate: true }\n)\nconst barObserever = useResizeObserver(barRef, () => update())\n\nonBeforeUnmount(() => {\n  saveObserver.forEach((observer) => observer.stop())\n  saveObserver.length = 0\n  barObserever.stop()\n})\n\ndefineExpose({\n  /** @description tab root html element */\n  ref: barRef,\n  /** @description method to manually update tab bar style */\n  update,\n})\n</script>\n"], "names": ["inject", "tabsRootContextKey", "throwError", "isUndefined", "capitalize", "useResizeObserver", "watch", "nextTick", "onBeforeUnmount", "_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;;;;;;;uCAmBc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAGA,IAAM,MAAA,QAAA,GAAWA,WAAOC,4BAAkB,CAAA,CAAA;AAC1C,IAAA,IAAI,CAAC,QAAA;AAEL,MAAMC,+BAAwB,EAAA,mCAAA,CAAA,CAAA;AAE9B,IAAA,MAAM,uBAA6B,CAAA,MAAA,CAAA,CAAA;AACnC,IAAA,MAAM,gBAA8B,EAAA,CAAA;AAEpC,IAAA,MAAM,kBAAc,EAAqB,CAAA;AACvC,IAAA,MAAI,WAAS,GAAA,MAAA;AACb,MAAA,IAAI,MAAU,GAAA,CAAA,CAAA;AAEd,MAAM,IAAA,OAAA,GAAA,CAAA,CAAA;AAGN,MAAM,MAAA,QAAA,GAAuB,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA,QAAgB,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,OAAA,GAAA,QAAA,CAAA;AAC7C,MAAM,MAAA,OAAA,GAAA,QAAuB,KAAA,OAAe,GAAA,GAAA,GAAA,GAAA,CAAA;AAE5C,MAAM,MAAA,QAAW,GAAA,OAAS,KAAA,GAAA,GAAA,MAAA,GAAA,KAAA,CAAA;AACxB,MAAA,KAAA,CAAI,IAAY,CAAA,KAAA,CAAA,CAAA,GAAA,KAAY;AAC5B,QAAA,IAAAC,iBAAY,CAAA,GAAc,CAAA,QAAA,CAAA;AAC1B,UAAI,OAAM,KAAO,CAAA;AAEjB,QAAI,SAAK,GAAQ,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA;AACf,QAAO,IAAA,CAAA,GAAA;AAAA,UACT,OAAA,KAAA,CAAA;AAEA,QAAA,IAAA,CAAA,GAAA,CAAS,MAAI,EAAA;AACb,UAAA,OAAA,IAAc,CAAA;AAEd,SAAM;AAEN,QAAA,oBAA0B,EAAAC,kBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACxB,QACE,OAAA,GAAA,GAAA,CAAA,CAAA,0BAA4B,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAE9B,QAAU,MAAA,SAAA,GAAA,MAAkB,CAAA,gBAAqB,CAAA,GAAA,CAAA,CAAA;AAAA,QACnD,IAAA,QAAA,KAAA,OAAA,EAAA;AACA,UAAO,OAAA,IAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,WAAA,CAAA,GAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,YAAA,CAAA,CAAA;AAAA,UACR,MAAA,IAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,WAAA,CAAA,CAAA;AAED,SAAO;AAAA,QACL,OAAC,KAAW,CAAA;AAAU,OAAA,CACtB;AAAoD,MACtD,OAAA;AAAA,QACF,CAAA,QAAA,GAAA,CAAA,EAAA,OAAA,CAAA,EAAA,CAAA;AAEA,QAAA,SAAe,EAAA,CAAA,SAAgB,EAAAA,kBAAA,CAAQ,OAAY,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,GAAA,CAAA;AAEnD,OAAA,CAAA;AACA,KAAA,CAAA;AACE,IAAA,MAAA,MAAA,GAAa,MAAQ,QAAc,CAAA,KAAA,GAAA,aAAc,CAAC;AAClD,IAAA,MAAA,YAAsB,GAAA,EAAA,CAAA;AAEtB,IAAA,MAAA,YAAoB,GAAA,MAAA;AAClB,MAAA,YAAA,CAAA,OAAkB,CAAA,CAAA,QAAA,KAAA,QAAuB,CAAA,IAAA,EAAO,CAAA,CAAA;AAAA,MAClD,YAAC,CAAA,MAAA,GAAA,CAAA,CAAA;AAAA,MACH,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAEA,QAAA,YAAA,CAAA,IAAA,CAAAC,sBAAA,CAAA,GAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAAA;AACc,KAAA,CACZ;AACE,IAAAC,SAAA,CAAA,MAAe,KAAA,CAAA,IAAA,EAAA,YAAA;AACf,MAAO,MAAAC,YAAA,EAAA,CAAA;AAEP,MAAa,MAAA,EAAA,CAAA;AAAA,MACf,YAAA,EAAA,CAAA;AAAA,KACA,EAAA,aAAkB,IAAA,EAAA,CAAA,CAAA;AAAA,IACpB,MAAA,YAAA,GAAAF,sBAAA,CAAA,MAAA,EAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AACA,IAAAG,mBAAqB,CAAA,MAAA;AAErB,MAAA,YAAA,CAAA,OAAsB,CAAA,CAAA,QAAA,KAAA,QAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACpB,MAAA,YAAA,CAAa,MAAQ,GAAC,CAAa,CAAA;AACnC,MAAA,YAAA,CAAa,IAAS,EAAA,CAAA;AACtB,KAAA,CAAA,CAAA;AAAkB,IACpB,MAAC,CAAA;AAED,MAAa,GAAA,EAAA,MAAA;AAAA,MAAA,MAAA;AAAA,KAAA,CAEX,CAAK;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAEL,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACD,OAAA,EAAA,QAAA;;;;;;;;;;;;"}