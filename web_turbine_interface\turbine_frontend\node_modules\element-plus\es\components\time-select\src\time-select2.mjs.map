{"version": 3, "file": "time-select2.mjs", "sources": ["../../../../../../packages/components/time-select/src/time-select.vue"], "sourcesContent": ["<template>\n  <el-select\n    ref=\"select\"\n    :model-value=\"value\"\n    :disabled=\"_disabled\"\n    :clearable=\"clearable\"\n    :clear-icon=\"clearIcon\"\n    :size=\"size\"\n    :effect=\"effect\"\n    :placeholder=\"placeholder\"\n    default-first-option\n    :filterable=\"editable\"\n    :empty-values=\"emptyValues\"\n    :value-on-clear=\"valueOnClear\"\n    @update:model-value=\"(event) => $emit(UPDATE_MODEL_EVENT, event)\"\n    @change=\"(event) => $emit(CHANGE_EVENT, event)\"\n    @blur=\"(event) => $emit('blur', event)\"\n    @focus=\"(event) => $emit('focus', event)\"\n    @clear=\"() => $emit('clear')\"\n  >\n    <el-option\n      v-for=\"item in items\"\n      :key=\"item.value\"\n      :label=\"item.value\"\n      :value=\"item.value\"\n      :disabled=\"item.disabled\"\n    />\n    <template #prefix>\n      <el-icon v-if=\"prefixIcon\" :class=\"nsInput.e('prefix-icon')\">\n        <component :is=\"prefixIcon\" />\n      </el-icon>\n    </template>\n  </el-select>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport ElSelect from '@element-plus/components/select'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport ElIcon from '@element-plus/components/icon'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { timeSelectProps } from './time-select'\nimport { compareTime, formatTime, nextTime, parseTime } from './utils'\n\ndayjs.extend(customParseFormat)\n\nconst { Option: ElOption } = ElSelect\n\ndefineOptions({\n  name: 'ElTimeSelect',\n})\n\ndefineEmits([CHANGE_EVENT, 'blur', 'focus', 'clear', UPDATE_MODEL_EVENT])\n\nconst props = defineProps(timeSelectProps)\n\nconst nsInput = useNamespace('input')\nconst select = ref<typeof ElSelect>()\n\nconst _disabled = useFormDisabled()\nconst { lang } = useLocale()\n\nconst value = computed(() => props.modelValue)\nconst start = computed(() => {\n  const time = parseTime(props.start)\n  return time ? formatTime(time) : null\n})\n\nconst end = computed(() => {\n  const time = parseTime(props.end)\n  return time ? formatTime(time) : null\n})\n\nconst step = computed(() => {\n  const time = parseTime(props.step)\n  return time ? formatTime(time) : null\n})\n\nconst minTime = computed(() => {\n  const time = parseTime(props.minTime || '')\n  return time ? formatTime(time) : null\n})\n\nconst maxTime = computed(() => {\n  const time = parseTime(props.maxTime || '')\n  return time ? formatTime(time) : null\n})\n\nconst items = computed(() => {\n  const result: { value: string; disabled: boolean }[] = []\n  const push = (formattedValue: string, rawValue: string) => {\n    result.push({\n      value: formattedValue,\n      disabled:\n        compareTime(rawValue, minTime.value || '-1:-1') <= 0 ||\n        compareTime(rawValue, maxTime.value || '100:100') >= 0,\n    })\n  }\n\n  if (props.start && props.end && props.step) {\n    let current = start.value\n    let currentTime: string\n    while (current && end.value && compareTime(current, end.value) <= 0) {\n      currentTime = dayjs(current, 'HH:mm')\n        .locale(lang.value)\n        .format(props.format)\n      push(currentTime, current)\n      current = nextTime(current, step.value!)\n    }\n    if (\n      props.includeEndTime &&\n      end.value &&\n      result[result.length - 1]?.value !== end.value\n    ) {\n      const formattedValue = dayjs(end.value, 'HH:mm')\n        .locale(lang.value)\n        .format(props.format)\n      push(formattedValue, end.value)\n    }\n  }\n  return result\n})\n\nconst blur = () => {\n  select.value?.blur?.()\n}\n\nconst focus = () => {\n  select.value?.focus?.()\n}\n\ndefineExpose({\n  /**\n   * @description focus the Input component\n   */\n  blur,\n  /**\n   * @description blur the Input component\n   */\n  focus,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref"], "mappings": ";;;;;;;;;;;;;mCAmDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AANA,IAAA,KAAA,CAAM,OAAO,iBAAiB,CAAA,CAAA;AAE9B,IAAM,MAAA,EAAE,MAAQ,EAAA,QAAA,EAAa,GAAA,QAAA,CAAA;AAU7B,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACpC,IAAA,MAAM,SAAS,GAAqB,EAAA,CAAA;AAEpC,IAAA,MAAM,YAAY,eAAgB,EAAA,CAAA;AAClC,IAAM,MAAA,EAAE,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAE3B,IAAA,MAAM,KAAQ,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,UAAU,CAAA,CAAA;AAC7C,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAM,MAAA,IAAA,GAAO,SAAU,CAAA,KAAA,CAAM,KAAK,CAAA,CAAA;AAClC,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,GAAA,GAAM,SAAS,MAAM;AACzB,MAAM,MAAA,IAAA,GAAO,SAAU,CAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAChC,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAO,SAAS,MAAM;AAC1B,MAAM,MAAA,IAAA,GAAO,SAAU,CAAA,KAAA,CAAM,IAAI,CAAA,CAAA;AACjC,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAA,MAAM,IAAO,GAAA,SAAA,CAAU,KAAM,CAAA,OAAA,IAAW,EAAE,CAAA,CAAA;AAC1C,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAA,MAAM,IAAO,GAAA,SAAA,CAAU,KAAM,CAAA,OAAA,IAAW,EAAE,CAAA,CAAA;AAC1C,MAAO,OAAA,IAAA,GAAO,UAAW,CAAA,IAAI,CAAI,GAAA,IAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AAED,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAA,IAAA,EAAM;AACN,MAAM,MAAA,MAAA,GAAQ,EAAA,CAAA;AACZ,MAAA,MAAA,IAAY,GAAA,CAAA,cAAA,EAAA,QAAA,KAAA;AAAA,QAAA,MACH,CAAA,IAAA,CAAA;AAAA,UACP,KACE,EAAA,cAAA;AACqD,UACxD,QAAA,EAAA,WAAA,CAAA,QAAA,EAAA,OAAA,CAAA,KAAA,IAAA,OAAA,CAAA,IAAA,CAAA,IAAA,WAAA,CAAA,QAAA,EAAA,OAAA,CAAA,KAAA,IAAA,SAAA,CAAA,IAAA,CAAA;AAAA,SACH,CAAA,CAAA;AAEA,OAAA,CAAA;AACE,MAAA,IAAA,eAAoB,KAAA,CAAA,GAAA,IAAA,KAAA,CAAA,IAAA,EAAA;AACpB,QAAI,IAAA,OAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AACJ,QAAO,IAAA,WAAA,CAAA;AACL,QAAc,OAAA,OAAA,IAAA,GAAA,CAAM,KAAS,IAAA,WAC1B,CAAA,SAAY,GAAK,CAAA,KACV,CAAA,IAAA,CAAA,EAAA;AACV,UAAA,mBAAyB,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACzB,UAAU,IAAA,CAAA,WAAA,EAAA,OAAkB,CAAA,CAAA;AAAW,UACzC,OAAA,GAAA,QAAA,CAAA,OAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,SACE;AAIA,QAAA,IAAA,KAAuB,CAAA,cAAA,IAAA,GAAM,CAAI,KAAA,IAAA,CAAO,CAAO,EAAA,GAAA,MACrC,CAAA,MAAA,CAAA,MAAU,GACV,CAAA,CAAA,KAAA,IAAA,GAAY,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,MAAA,GAAA,CAAA,KAAA,EAAA;AACtB,UAAK,MAAA,cAAA,QAAyB,CAAA,GAAA,CAAA,KAAA,EAAA,OAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAAA,UAChC,IAAA,CAAA,cAAA,EAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SACF;AACA,OAAO;AAAA,MACR,OAAA,MAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,aAAqB;AAAA,MACvB,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAA,CAAA,EAAA,SAAc,MAAM,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AAAsB,IACxB,MAAA,KAAA,GAAA,MAAA;AAEA,MAAa,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAIX,IAAA;AAAA,MAAA,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAIA,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,QAAA,CAAA,EAAA;AAAA,QACD,OAAA,EAAA,QAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}