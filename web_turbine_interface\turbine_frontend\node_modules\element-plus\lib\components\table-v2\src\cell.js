'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var common = require('./common.js');
var runtime = require('../../../utils/vue/props/runtime.js');

const tableV2CellProps = runtime.buildProps({
  class: String,
  cellData: {
    type: runtime.definePropType([String, Boolean, Number, Object])
  },
  column: common.column,
  columnIndex: Number,
  style: {
    type: runtime.definePropType([String, Array, Object])
  },
  rowData: {
    type: runtime.definePropType(Object)
  },
  rowIndex: Number
});

exports.tableV2CellProps = tableV2CellProps;
//# sourceMappingURL=cell.js.map
