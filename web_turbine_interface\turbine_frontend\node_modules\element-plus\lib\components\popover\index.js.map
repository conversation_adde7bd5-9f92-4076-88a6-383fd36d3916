{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/popover/index.ts"], "sourcesContent": ["import { withInstall, withInstallDirective } from '@element-plus/utils'\nimport Popover from './src/popover.vue'\nimport PopoverDirective, { VPopover } from './src/directive'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPopoverDirective: SFCWithInstall<typeof PopoverDirective> =\n  withInstallDirective(PopoverDirective, VPopover)\n\nexport const ElPopover: SFCWithInstall<typeof Popover> & {\n  directive: typeof ElPopoverDirective\n} = withInstall(Popover, {\n  directive: ElPopoverDirective,\n})\nexport default ElPopover\n\nexport * from './src/popover'\n"], "names": ["withInstallDirective", "PopoverDirective", "VPopover", "withInstall", "Popover"], "mappings": ";;;;;;;;;AAGY,MAAC,kBAAkB,GAAGA,4BAAoB,CAACC,oBAAgB,EAAEC,kBAAQ,EAAE;AACvE,MAAC,SAAS,GAAGC,mBAAW,CAACC,oBAAO,EAAE;AAC9C,EAAE,SAAS,EAAE,kBAAkB;AAC/B,CAAC;;;;;;;;"}