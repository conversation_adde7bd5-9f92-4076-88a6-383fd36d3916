{"version": 3, "file": "hr.js", "sources": ["../../../../../packages/locale/lang/hr.ts"], "sourcesContent": ["export default {\n  name: 'hr',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Odaberi datum',\n      selectTime: 'Odaberi vrijeme',\n      startDate: 'Datum početka',\n      startTime: 'Vri<PERSON>me početka',\n      endDate: 'Datum završetka',\n      endTime: 'Vrijeme završetka',\n      prevYear: 'Preth<PERSON>na godina',\n      nextYear: 'Sljedeća godina',\n      prevMonth: 'Prethodni mjesec',\n      nextMonth: 'Slje<PERSON>ći mjesec',\n      year: '',\n      month1: 'Siječanj',\n      month2: 'Veljača',\n      month3: 'O<PERSON><PERSON><PERSON>',\n      month4: 'Travanj',\n      month5: 'Svibanj',\n      month6: '<PERSON>panj',\n      month7: 'Srpanj',\n      month8: '<PERSON><PERSON><PERSON>',\n      month9: 'Rujan',\n      month10: 'Listopad',\n      month11: '<PERSON><PERSON><PERSON>',\n      month12: 'Prosinac',\n      week: 'tjedan',\n      weeks: {\n        sun: 'Ned',\n        mon: 'Pon',\n        tue: 'Uto',\n        wed: 'Sri',\n        thu: 'Čet',\n        fri: 'Pet',\n        sat: 'Sub',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Učitavanje',\n      noMatch: 'Nema pronađenih podataka',\n      noData: 'Nema podataka',\n      placeholder: 'Izaberi',\n    },\n    mention: {\n      loading: 'Učitavanje',\n    },\n    cascader: {\n      noMatch: 'Nema pronađenih podataka',\n      loading: 'Učitavanje',\n      placeholder: 'Izaberi',\n      noData: 'Nema podataka',\n    },\n    pagination: {\n      goto: 'Idi na',\n      pagesize: '/stranica',\n      total: 'Ukupno {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Poruka',\n      confirm: 'OK',\n      cancel: 'Otkaži',\n      error: 'Pogrešan unos',\n    },\n    upload: {\n      deleteTip: 'pritisnite izbriši za brisanje',\n      delete: 'Izbriši',\n      preview: 'Pregled',\n      continue: 'Nastavak',\n    },\n    table: {\n      emptyText: 'Nema podataka',\n      confirmFilter: 'Potvrdi',\n      resetFilter: 'Resetiraj',\n      clearFilter: 'Sve',\n      sumText: 'Suma',\n    },\n    tree: {\n      emptyText: 'Nema podataka',\n    },\n    transfer: {\n      noMatch: 'Nema pronađenih podataka',\n      noData: 'Nema podataka',\n      titles: ['Lista 1', 'Lista 2'], // to be translated\n      filterPlaceholder: 'Unesite ključnu riječ', // to be translated\n      noCheckedFormat: '{total} stavki', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,QAAQ,EAAE,sBAAsB;AACtC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,sBAAsB;AACvC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,iBAAiB;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,eAAe;AAC7B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,WAAW;AAC3B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,qCAAqC;AACtD,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,aAAa,EAAE,SAAS;AAC9B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,eAAe;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,iCAAiC;AAC1D,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}