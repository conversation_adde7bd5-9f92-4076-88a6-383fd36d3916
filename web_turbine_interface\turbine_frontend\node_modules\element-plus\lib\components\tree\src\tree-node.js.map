{"version": 3, "file": "tree-node.js", "sources": ["../../../../../../packages/components/tree/src/tree-node.vue"], "sourcesContent": ["<template>\n  <div\n    v-show=\"node.visible\"\n    ref=\"node$\"\n    :class=\"[\n      ns.b('node'),\n      ns.is('expanded', expanded),\n      ns.is('current', node.isCurrent),\n      ns.is('hidden', !node.visible),\n      ns.is('focusable', !node.disabled),\n      ns.is('checked', !node.disabled && node.checked),\n      getNodeClass(node),\n    ]\"\n    role=\"treeitem\"\n    tabindex=\"-1\"\n    :aria-expanded=\"expanded\"\n    :aria-disabled=\"node.disabled\"\n    :aria-checked=\"node.checked\"\n    :draggable=\"tree.props.draggable\"\n    :data-key=\"getNodeKey(node)\"\n    @click.stop=\"handleClick\"\n    @contextmenu=\"handleContextMenu\"\n    @dragstart.stop=\"handleDragStart\"\n    @dragover.stop=\"handleDragOver\"\n    @dragend.stop=\"handleDragEnd\"\n    @drop.stop=\"handleDrop\"\n  >\n    <div\n      :class=\"ns.be('node', 'content')\"\n      :style=\"{ paddingLeft: (node.level - 1) * tree.props.indent + 'px' }\"\n    >\n      <el-icon\n        v-if=\"tree.props.icon || CaretRight\"\n        :class=\"[\n          ns.be('node', 'expand-icon'),\n          ns.is('leaf', node.isLeaf),\n          {\n            expanded: !node.isLeaf && expanded,\n          },\n        ]\"\n        @click.stop=\"handleExpandIconClick\"\n      >\n        <component :is=\"tree.props.icon || CaretRight\" />\n      </el-icon>\n      <el-checkbox\n        v-if=\"showCheckbox\"\n        :model-value=\"node.checked\"\n        :indeterminate=\"node.indeterminate\"\n        :disabled=\"!!node.disabled\"\n        @click.stop\n        @change=\"handleCheckChange\"\n      />\n      <el-icon\n        v-if=\"node.loading\"\n        :class=\"[ns.be('node', 'loading-icon'), ns.is('loading')]\"\n      >\n        <loading />\n      </el-icon>\n      <node-content :node=\"node\" :render-content=\"renderContent\" />\n    </div>\n    <el-collapse-transition>\n      <div\n        v-if=\"!renderAfterExpand || childNodeRendered\"\n        v-show=\"expanded\"\n        :class=\"ns.be('node', 'children')\"\n        role=\"group\"\n        :aria-expanded=\"expanded\"\n        @click.stop\n      >\n        <el-tree-node\n          v-for=\"child in node.childNodes\"\n          :key=\"getNodeKey(child)\"\n          :render-content=\"renderContent\"\n          :render-after-expand=\"renderAfterExpand\"\n          :show-checkbox=\"showCheckbox\"\n          :node=\"child\"\n          :accordion=\"accordion\"\n          :props=\"props\"\n          @node-expand=\"handleChildNodeExpand\"\n        />\n      </div>\n    </el-collapse-transition>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  nextTick,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { debugWarn, isFunction, isString } from '@element-plus/utils'\nimport ElCollapseTransition from '@element-plus/components/collapse-transition'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CaretRight, Loading } from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport NodeContent from './tree-node-content.vue'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { dragEventsKey } from './model/useDragNode'\nimport Node from './model/node'\nimport { NODE_INSTANCE_INJECTION_KEY, ROOT_TREE_INJECTION_KEY } from './tokens'\n\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { RootTreeType, TreeNodeData, TreeOptionProps } from './tree.type'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\n\nexport default defineComponent({\n  name: 'ElTreeNode',\n  components: {\n    ElCollapseTransition,\n    ElCheckbox,\n    NodeContent,\n    ElIcon,\n    Loading,\n  },\n  props: {\n    node: {\n      type: Node,\n      default: () => ({}),\n    },\n    props: {\n      type: Object as PropType<TreeOptionProps>,\n      default: () => ({}),\n    },\n    accordion: Boolean,\n    renderContent: Function,\n    renderAfterExpand: Boolean,\n    showCheckbox: Boolean,\n  },\n  emits: ['node-expand'],\n  setup(props, ctx) {\n    const ns = useNamespace('tree')\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n    const tree = inject<RootTreeType>(ROOT_TREE_INJECTION_KEY)!\n    const expanded = ref(false)\n    const childNodeRendered = ref(false)\n    const oldChecked = ref<boolean>()\n    const oldIndeterminate = ref<boolean>()\n    const node$ = ref<HTMLElement>()\n    const dragEvents = inject(dragEventsKey)!\n    const instance = getCurrentInstance()\n\n    provide(NODE_INSTANCE_INJECTION_KEY, instance)\n    if (!tree) {\n      debugWarn('Tree', \"Can not find node's tree.\")\n    }\n\n    if (props.node.expanded) {\n      expanded.value = true\n      childNodeRendered.value = true\n    }\n\n    const childrenKey = tree.props.props['children'] || 'children'\n    watch(\n      () => {\n        const children = props.node.data?.[childrenKey]\n        return children && [...children]\n      },\n      () => {\n        props.node.updateChildren()\n      }\n    )\n\n    watch(\n      () => props.node.indeterminate,\n      (val) => {\n        handleSelectChange(props.node.checked, val)\n      }\n    )\n\n    watch(\n      () => props.node.checked,\n      (val) => {\n        handleSelectChange(val, props.node.indeterminate)\n      }\n    )\n\n    watch(\n      () => props.node.childNodes.length,\n      () => props.node.reInitChecked()\n    )\n\n    watch(\n      () => props.node.expanded,\n      (val) => {\n        nextTick(() => (expanded.value = val))\n        if (val) {\n          childNodeRendered.value = true\n        }\n      }\n    )\n\n    const getNodeKey = (node: Node): any => {\n      return getNodeKeyUtil(tree.props.nodeKey, node.data)\n    }\n\n    const getNodeClass = (node: Node) => {\n      const nodeClassFunc = props.props.class\n      if (!nodeClassFunc) {\n        return {}\n      }\n      let className\n      if (isFunction(nodeClassFunc)) {\n        const { data } = node\n        className = nodeClassFunc(data, node)\n      } else {\n        className = nodeClassFunc\n      }\n\n      if (isString(className)) {\n        return { [className]: true }\n      } else {\n        return className\n      }\n    }\n\n    const handleSelectChange = (checked: boolean, indeterminate: boolean) => {\n      if (\n        oldChecked.value !== checked ||\n        oldIndeterminate.value !== indeterminate\n      ) {\n        tree.ctx.emit('check-change', props.node.data, checked, indeterminate)\n      }\n      oldChecked.value = checked\n      oldIndeterminate.value = indeterminate\n    }\n\n    const handleClick = (e: MouseEvent) => {\n      handleCurrentChange(tree.store, tree.ctx.emit, () => {\n        const nodeKeyProp = tree?.props?.nodeKey\n        if (nodeKeyProp) {\n          const curNodeKey = getNodeKey(props.node)\n          tree.store.value.setCurrentNodeKey(curNodeKey)\n        } else {\n          tree.store.value.setCurrentNode(props.node)\n        }\n      })\n      tree.currentNode.value = props.node\n\n      if (tree.props.expandOnClickNode) {\n        handleExpandIconClick()\n      }\n\n      if (\n        (tree.props.checkOnClickNode ||\n          (props.node.isLeaf &&\n            tree.props.checkOnClickLeaf &&\n            props.showCheckbox)) &&\n        !props.node.disabled\n      ) {\n        handleCheckChange(!props.node.checked)\n      }\n      tree.ctx.emit('node-click', props.node.data, props.node, instance, e)\n    }\n\n    const handleContextMenu = (event: Event) => {\n      if (tree.instance.vnode.props?.['onNodeContextmenu']) {\n        event.stopPropagation()\n        event.preventDefault()\n      }\n      tree.ctx.emit(\n        'node-contextmenu',\n        event,\n        props.node.data,\n        props.node,\n        instance\n      )\n    }\n\n    const handleExpandIconClick = () => {\n      if (props.node.isLeaf) return\n      if (expanded.value) {\n        tree.ctx.emit('node-collapse', props.node.data, props.node, instance)\n        props.node.collapse()\n      } else {\n        props.node.expand(() => {\n          ctx.emit('node-expand', props.node.data, props.node, instance)\n        })\n      }\n    }\n\n    const handleCheckChange = (value: CheckboxValueType) => {\n      props.node.setChecked(value as boolean, !tree?.props.checkStrictly)\n      nextTick(() => {\n        const store = tree.store.value\n        tree.ctx.emit('check', props.node.data, {\n          checkedNodes: store.getCheckedNodes(),\n          checkedKeys: store.getCheckedKeys(),\n          halfCheckedNodes: store.getHalfCheckedNodes(),\n          halfCheckedKeys: store.getHalfCheckedKeys(),\n        })\n      })\n    }\n\n    const handleChildNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      tree.ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const handleDragStart = (event: DragEvent) => {\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragStart({ event, treeNode: props })\n    }\n\n    const handleDragOver = (event: DragEvent) => {\n      event.preventDefault()\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragOver({\n        event,\n        treeNode: { $el: node$.value, node: props.node },\n      })\n    }\n\n    const handleDrop = (event: DragEvent) => {\n      event.preventDefault()\n    }\n\n    const handleDragEnd = (event: DragEvent) => {\n      if (!tree.props.draggable) return\n      dragEvents.treeNodeDragEnd(event)\n    }\n\n    return {\n      ns,\n      node$,\n      tree,\n      expanded,\n      childNodeRendered,\n      oldChecked,\n      oldIndeterminate,\n      getNodeKey,\n      getNodeClass,\n      handleSelectChange,\n      handleClick,\n      handleContextMenu,\n      handleExpandIconClick,\n      handleCheckChange,\n      handleChildNodeExpand,\n      handleDragStart,\n      handleDragOver,\n      handleDrop,\n      handleDragEnd,\n      CaretRight,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElCollapseTransition", "ElCheckbox", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElIcon", "Loading", "Node", "useNamespace", "useNodeExpandEventBroadcast", "inject", "ROOT_TREE_INJECTION_KEY", "ref", "dragEventsKey", "getCurrentInstance", "provide", "NODE_INSTANCE_INJECTION_KEY", "debugWarn", "watch", "nextTick", "getNodeKeyUtil", "isFunction", "isString", "handleCurrentChange", "CaretRight", "_resolveComponent", "_withDirectives", "_openBlock", "_createElementBlock", "_normalizeClass", "_withModifiers", "_createElementVNode", "_normalizeStyle", "_createBlock", "_withCtx", "_resolveDynamicComponent", "_createCommentVNode", "_createVNode", "_Fragment", "_renderList", "_vShow", "_export_sfc"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgHA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,YAAA;AAAA,EACN,UAAY,EAAA;AAAA,0BACVC,0BAAA;AAAA,gBACAC,kBAAA;AAAA,iBACAC,0BAAA;AAAA,YACAC,cAAA;AAAA,aACAC,gBAAA;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAAC,eAAA;AAAA,MACN,OAAA,EAAS,OAAO,EAAC,CAAA;AAAA,KACnB;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,OAAA,EAAS,OAAO,EAAC,CAAA;AAAA,KACnB;AAAA,IACA,SAAW,EAAA,OAAA;AAAA,IACX,aAAe,EAAA,QAAA;AAAA,IACf,iBAAmB,EAAA,OAAA;AAAA,IACnB,YAAc,EAAA,OAAA;AAAA,GAChB;AAAA,EACA,KAAA,EAAO,CAAC,aAAa,CAAA;AAAA,EACrB,KAAA,CAAM,OAAO,GAAK,EAAA;AAChB,IAAM,MAAA,EAAA,GAAKC,qBAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,EAAE,iBAAA,EAAsB,GAAAC,uDAAA,CAA4B,KAAK,CAAA,CAAA;AAC/D,IAAM,MAAA,IAAA,GAAOC,WAAqBC,8BAAuB,CAAA,CAAA;AACzD,IAAM,MAAA,QAAA,GAAWC,QAAI,KAAK,CAAA,CAAA;AAC1B,IAAM,MAAA,iBAAA,GAAoBA,QAAI,KAAK,CAAA,CAAA;AACnC,IAAA,MAAM,aAAaA,OAAa,EAAA,CAAA;AAChC,IAAA,MAAM,mBAAmBA,OAAa,EAAA,CAAA;AACtC,IAAA,MAAM,QAAQA,OAAiB,EAAA,CAAA;AAC/B,IAAM,MAAA,UAAA,GAAaF,WAAOG,yBAAa,CAAA,CAAA;AACvC,IAAA,MAAM,WAAWC,sBAAmB,EAAA,CAAA;AAEpC,IAAAC,WAAA,CAAQC,oCAA6B,QAAQ,CAAA,CAAA;AAC7C,IAAA,IAAI,CAAC,IAAM,EAAA;AACT,MAAAC,eAAA,CAAU,QAAQ,2BAA2B,CAAA,CAAA;AAAA,KAC/C;AAEA,IAAI,IAAA,KAAA,CAAM,KAAK,QAAU,EAAA;AACvB,MAAA,QAAA,CAAS,KAAQ,GAAA,IAAA,CAAA;AACjB,MAAA,iBAAA,CAAkB,KAAQ,GAAA,IAAA,CAAA;AAAA,KAC5B;AAEA,IAAA,MAAM,WAAc,GAAA,IAAA,CAAK,KAAM,CAAA,KAAA,CAAM,UAAU,CAAK,IAAA,UAAA,CAAA;AACpD,IAAAC,SAAA,CAAA,MAAA;AAAA,MACE,IAAM,EAAA,CAAA;AACJ,MAAA,MAAA,QAAiB,GAAA,CAAA,EAAA,GAAA,KAAW,CAAA,IAAA,CAAA,IAAkB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,CAAA,CAAA;AAC9C,MAAO,OAAA,QAAA,IAAA,CAAA,GAAa,QAAW,CAAA,CAAA;AAAA,KACjC,EAAA,MAAA;AAAA,MACA,KAAM,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA;AACJ,KAAA,CAAA,CAAA;AAA0B,IAC5BA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,aAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACF,kBAAA,CAAA,KAAA,CAAA,IAAA,CAAA,OAAA,EAAA,GAAA,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IACEA,SAAA,CAAA,WAAiB,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACjB,kBAAS,CAAA,GAAA,EAAA,KAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;AACP,KAAmB,CAAA,CAAA;AAAuB,IAC5CA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,EAAA,MAAA,KAAA,CAAA,IAAA,CAAA,aAAA,EAAA,CAAA,CAAA;AAAA,IACFA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,GAAA,KAAA;AAEA,MAAAC,YAAA,CAAA,MAAA,QAAA,CAAA,KAAA,GAAA,GAAA,CAAA,CAAA;AAAA,MACE,IAAA;AAAiB,QACR,iBAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACP,OAAmB;AAA6B,KAClD,CAAA,CAAA;AAAA,IACF,MAAA,UAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAA,OAAAC,eAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAAA,KACE,CAAA;AAA4B,IAC5B,MAAA,YAAY,GAAmB,CAAA,IAAA,KAAA;AAAA,MACjC,MAAA,aAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAEA,MAAA,IAAA,CAAA,aAAA,EAAA;AAAA,QACE,UAAY;AAAK,OAChB;AACC,MAAS,IAAA,SAAA,CAAA;AACT,MAAA,IAAAC,iBAAS,CAAA,aAAA,CAAA,EAAA;AACP,QAAA,MAAA,EAAA,IAAA,EAAA,GAAA,IAA0B,CAAA;AAAA,QAC5B,SAAA,GAAA,aAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OACF,MAAA;AAAA,QACF,SAAA,GAAA,aAAA,CAAA;AAEA,OAAM;AACJ,MAAA,IAAAC,eAAsB,CAAA,SAAA,CAAA,EAAA;AAA6B,QACrD,OAAA,EAAA,CAAA,SAAA,GAAA,IAAA,EAAA,CAAA;AAEA,OAAM,MAAA;AACJ,QAAM,OAAA,SAAA,CAAA;AACN,OAAA;AACE,KAAA,CAAA;AAAQ,IACV,MAAA,kBAAA,GAAA,CAAA,OAAA,EAAA,aAAA,KAAA;AACA,MAAI,IAAA,UAAA,CAAA,KAAA,KAAA,OAAA,IAAA,gBAAA,CAAA,KAAA,KAAA,aAAA,EAAA;AACJ,QAAI,IAAA,CAAA,GAAA,CAAA,IAAW,eAAgB,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,EAAA,aAAA,CAAA,CAAA;AAC7B,OAAM;AACN,MAAY,UAAA,CAAA,KAAA,GAAA,OAAA,CAAA;AAAwB,MACtC,gBAAO,CAAA,KAAA,GAAA,aAAA,CAAA;AACL,KAAY,CAAA;AAAA,IACd,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAEA,MAAIC,6BAAqB,CAAA,KAAA,EAAA,IAAA,CAAA,GAAA,CAAA,IAAA,EAAA,MAAA;AACvB,QAAA,IAAA,EAAA,CAAO;AAAoB,QACtB,MAAA,WAAA,GAAA,CAAA,EAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,CAAA;AACL,QAAO,IAAA,WAAA,EAAA;AAAA,UACT,MAAA,UAAA,GAAA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,UACF,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,CAAA;AAEA,SAAM,MAAA;AACJ,UACE,IAAW,CAAA,KAAA,CAAA,KAAA,CAAA,cACX,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,SAAA;AAAqE,OACvE,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAmB,CAAA,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AACnB,MAAA,IAAA,IAAA,CAAA,KAAA,CAAA,iBAAyB,EAAA;AAAA,QAC3B,qBAAA,EAAA,CAAA;AAEA,OAAM;AACJ,MAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,gBAAgC,IAAK,KAAI,MAAM,MAAM,IAAA,IAAA,CAAA,KAAA,CAAA,gBAAA,IAAA,KAAA,CAAA,YAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACnD,QAAM,iBAAA,CAAA,CAAA,UAA2B,CAAA,OAAA,CAAA,CAAA;AACjC,OAAA;AACE,MAAM,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,YAAwB,EAAA,KAAA,CAAA,IAAA,CAAA,IAAU,EAAA,KAAA,CAAA,IAAA,EAAA,QAAA,EAAA,CAAA,CAAA,CAAA;AACxC,KAAK,CAAA;AAAwC,IAAA,MACxC,iBAAA,GAAA,CAAA,KAAA,KAAA;AACL,MAAA,IAAA,EAAA,CAAA;AAA0C,MAC5C,IAAA,CAAA,EAAA,GAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA;AAAA,QACD,KAAA,CAAA,eAAA,EAAA,CAAA;AACD,QAAK,KAAA,CAAA,iBAAoB;AAEzB,OAAI;AACF,MAAsB,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,kBAAA,EAAA,KAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAAA,KACxB,CAAA;AAEA,IAAA,MAAA,qBAEK,GAAA,MAAA;AAKH,MAAkB,IAAA,KAAA,CAAA,IAAA,CAAA,MAAC;AAAkB,QACvC,OAAA;AACA,MAAK,IAAA,cAAuB,EAAA;AAAwC,QACtE,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,eAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAEA,QAAM,KAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAoB;AACxB,OAAA,MAAS;AACP,QAAA,KAAA,CAAM,IAAgB,CAAA,MAAA,CAAA,MAAA;AACtB,UAAA,GAAA,CAAM,IAAe,CAAA,aAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AACA,OAAA;AAAS,KACP,CAAA;AAAA,IACA,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MAAA,UACW,CAAA,UAAA,CAAA,KAAA,EAAA,EAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,CAAA;AAAA,MAAAJ,YACL,CAAA,MAAA;AAAA,QACN,MAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,QACF,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AAAA,UACF,YAAA,EAAA,KAAA,CAAA,eAAA,EAAA;AAEA,UAAM,iCAA8B,EAAA;AAClC,UAAI,gBAAmB,EAAA,KAAA,CAAA,mBAAA,EAAA;AACvB,UAAI,eAAgB,EAAA,KAAA,CAAA,kBAAA,EAAA;AAClB,SAAK,CAAA,CAAA;AACL,OAAA,CAAA,CAAA;AAAoB,KAAA,CACtB;AACE,IAAM,MAAA,qBAAkB,GAAA,CAAA,QAAA,EAAA,IAAA,EAAA,SAAA,KAAA;AACtB,MAAA,iBAAwB,CAAA,IAAA,CAAA,CAAA;AAAqC,MAAA,IAC9D,CAAA,GAAA,CAAA,IAAA,CAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAA,CAAA,CAAA;AAAA,KACH,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAqB;AACzB,QAAA;AACA,MAAA,UAAe,CAAA,iBAAA,CAAA,EAAA,KAAA,EAAA,QAAA,EAAA,KAAA,EAAA,CAAA,CAAA;AACb,KAAM,CAAA;AACN,IAAA,MAAA,cAAuB,GAAA,CAAA,KAAA;AAAiB,MACtC,KAAA,CAAA;AAAoC,MACpC,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAkC;AAAA,QAClC,OAAA;AAA4C,MAC5C,UAAA,CAAA,gBAAuB,CAAmB;AAAA,QAC5C,KAAC;AAAA,QACF,QAAA,EAAA,EAAA,GAAA,EAAA,KAAA,CAAA,KAAA,EAAA,IAAA,EAAA,KAAA,CAAA,IAAA,EAAA;AAAA,OACH,CAAA,CAAA;AAEA,KAAA,CAAA;AAKE,IAAA,MAAA,UAAA,GAAA,CAAkB,KAAI,KAAA;AACtB,MAAA,KAAK,CAAI,cAAoB,EAAA,CAAA;AAAwB,KACvD,CAAA;AAEA,IAAM,MAAA,aAAA,GAAA,CAAA,KAAwC,KAAA;AAC5C,MAAI,IAAA,CAAC,IAAK,CAAA,KAAA,CAAM,SAAW;AAC3B,QAAA,OAAA;AAAuD,MACzD,UAAA,CAAA,eAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,OAAA;AACA,MAAI,EAAA;AACJ,MAAA,KAAA;AAA4B,MAC1B,IAAA;AAAA,MAAA;AAC+C,MACjD,iBAAC;AAAA,MACH,UAAA;AAEA,MAAM,gBAAA;AACJ,MAAA,UAAqB;AAAA,MACvB,YAAA;AAEA,MAAM,kBAAA;AACJ,MAAI,WAAM;AACV,MAAA;AAAgC,MAClC,qBAAA;AAEA,MAAO,iBAAA;AAAA,MACL,qBAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,MACA,UAAA;AAAA,MACA,aAAA;AAAA,kBACAK,mBAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,MAAA,kBAAA,GAAAC,oBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,EACA,MAAA,sBAAA,GAAAA,oBAAA,CAAA,aAAA,CAAA,CAAA;AAAA,EACA,MAAA,kBAAA,GAAAA,oBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,EACA,MAAA,uBAAA,GAAAA,oBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EACA,MAAA,uBAAA,GAAAA,oBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EACA,MAAA,iCAAA,GAAAA,oBAAA,CAAA,wBAAA,CAAA,CAAA;AAAA,EACA,OAAAC,kBAAA,EAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,IACA,GAAA,EAAA,OAAA;AAAA,IACA,KAAA,EAAAC,kBAAA,CAAA;AAAA,MACF,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,MACF,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,CAAA;AACF,MAAC,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA;;;;;;;;;;AAjRO,IA/EJ,cAAI,EAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AAAA,IACH,SAAK,EAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA;AAAA,IAAA,UAAa,EAAC,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AAAA,IAAgB,OAAA,EAAAC,iBAAK,CAAA,IAAqB,CAAA,WAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IAAA,aAAc,EAAY,IAAA,CAAA,iBAAc;AAAA,IAAA,WAAY,EAAEA,iBAAY,CAAA,IAAA,CAAA,eAAY,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IAAA,UAAY,EAAAA,iBAAiB,CAAA,IAAA,CAAA,cAAa,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IAAA,WAAcA,iBAAa,CAAK,IAAA,CAAA,aAAA,EAAA,CAAY;AAAY,IAAA,yBAA0B,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IASxQC,sBAAK,CAAA,KAAA,EAAA;AAAA,MACI,KAAA,EAAAF,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,MACO,KAAA,EAAAG,kBAAA,CAAA,EAAA,WAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,GAAA,IAAA,EAAA,CAAA;AAAA;AACK,qBACD,CAAA,IAAA,IAAA,IAAA,CAAA,UAAA,IAAAL,aAAA,EAAA,EAAAM,eAAA,CAAA,kBAAA,EAAA;AAAA,QACnB,GAAA,EAAA,CAAA;AAAsB,QACtB,KAAA,EAAAJ;AAAyB,UACzB,mBAAuB,aAAA,CAAA;AAAA,UACV,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AAAA,UACb;AAA+B,YAC/B,oBAA6B,CAAA,MAAA,IAAA,IAAA,CAAA,QAAA;AAAA,WAC7B;AAA2B,SAC3B,CAAA;AAAqB,QAAA,OAAA,EAAAC,iBAAA,CAAA,IAAA,CAAA,qBAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAEtB,OAAA,EAAA;AAAA,QAgCM,OAAA,EAAAI,WAAA,CAAA,MAAA;AAAA,WAAAP,aAAA,EAAA,EAAAM,eAAA,CAAAE,2BAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,IAAA,IAAA,CAAA,UAAA,CAAA,CAAA;AAAA,SA/BH,CAAA;AAAY,QACZ;AAA0D,OAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,CAAA,IAAAC,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;QAGnD,GAAK,EAAA,CAAA;AAWH,QAAA,aAAA,EAAA,IAAA,CAAA,IAAA,CAAA,OAAA;qBAVF,EAAA,IAAA,CAAA,IAAA,CAAA,aAAA;AAAA,QAAA,YAAiB,IAAE,CAAA,IAAA,CAAA,QAAA;AAAA,QAAA,OAAsC,EAAAN,iBAAW,CAAA,MAAA;AAAW,SAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAsC,QAAA,QAAA,EAAA,IAAA,CAAA,iBAAe;AAAA,OAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,SAAA,EAAA,UAAA,CAAA,CAAA,IAAAM,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;AAOzI,QAAA,GAAA,EAAA,CAAA;AAAiC,QAAA,KAAA,EAAAP,kBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,cAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;AAEe,QAAA,OAAA,EAAAK,WAAA,CAAA,MAAA;AAAJ,UAAAG,eAAA,CAAA,kBAAA,CAAA;;;;AAGvC,MAAAA,eAAA,CAAA,uBAAA,EAMN;AAAA,QAAA,IAAA,EAAA,IAAA,CAAA,IAAA;AALC,QAAA,gBAAA,EAAkB,IAAA,CAAA,aAAA;AAAA,OAAA,EAAA,gBACH,EAAK,gBAAA,CAAA,CAAA;AAAA,KACpB,EAAA,CAAA,CAAA;AAAiB,IAAAA,iDAClB,EAAA,IAAA,EAAA;AAAA,MAAW,OAAA,EAAAH,WAAA,CAAA,MAAA;AAAA,QAAA,CAAA,IACF,CAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,GAAAR,kBAAA,EAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,UAAA,GAAA,EAAA,CAAA;AAGH,UAAA,KAAA,EAAAC,6BADR,CAKU,MAAA,EAAA,UAAA,CAAA,CAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAHP,UAAA,eAAQ,EAAA,IAAA,CAAA,QAAA;AAAoC,UAAA,OAAA,EAAAC,iBAAA,CAAA,MAAA;;AAElC,SAAA,EAAA;AAAA,WAAAH,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAU,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,KAAA,KAAA;;;;cAEgD,qBAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAxC,eAAA,EAAA,IAAA,CAAA,YAAA;AAAA,cAAuB,IAAA,EAAA,KAAA;AAAA,cAAA,SAAA,EAAA,IAAA,CAAA,SAAA;;;;;SAuBrB,EAAA,EAAA,EAAA,CAAA,eAAA,EAAA,SAAA,CAAA,CAAA,GAAA;AAAA,UAAA,CAAAC,SAAA,EAAA,IAAA,CApBvB,QAmBM,CAAA;AAAA,SAlBG,CAAA,GAAAJ,sBAAA,CAAA;AAkBH,OAAA,CAAA;UAhBH;AAAY,KAAA,CAAA;AACR,GAAA,EAAA,EAAA,EAAA,CACJ,eAAe,EAAA,eAAA,EAAA,cAAA,EAAA,WAAA,EAAA,UAAA,EAAA,SAAA,EAAA,eAAA,EAAA,aAAA,EAAA,YAAA,EAAA,WAAA,EAAA,QAAA,CAAA,CAAA,GAAA;AAAA,IAAA,CAAAI,oBAChB,CAAA,OAAA,CAAA;AAAA,GAAW,CAAA,CAAA;AAAA,CAAA;AAEX,iBAUE,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,eAAA,CAAA,CAAA,CAAA;;;;"}