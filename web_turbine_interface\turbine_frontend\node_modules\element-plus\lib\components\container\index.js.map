{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/container/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\nimport Container from './src/container.vue'\nimport Aside from './src/aside.vue'\nimport Footer from './src/footer.vue'\nimport Header from './src/header.vue'\nimport Main from './src/main.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElContainer: SFCWithInstall<typeof Container> & {\n  Aside: typeof Aside\n  Footer: typeof Footer\n  Header: typeof Header\n  Main: typeof Main\n} = withInstall(Container, {\n  Aside,\n  Footer,\n  Header,\n  Main,\n})\n\nexport default ElContainer\nexport const ElAside: SFCWithInstall<typeof Aside> = withNoopInstall(Aside)\nexport const ElFooter: SFCWithInstall<typeof Footer> = withNoopInstall(Footer)\nexport const ElHeader: SFCWithInstall<typeof Header> = withNoopInstall(Header)\nexport const ElMain: SFCWithInstall<typeof Main> = withNoopInstall(Main)\n\nexport type ContainerInstance = InstanceType<typeof Container> & unknown\nexport type AsideInstance = InstanceType<typeof Aside> & unknown\nexport type FooterInstance = InstanceType<typeof Footer> & unknown\nexport type HeaderInstance = InstanceType<typeof Header> & unknown\nexport type MainInstance = InstanceType<typeof Main> & unknown\n"], "names": ["withInstall", "Container", "Aside", "Footer", "Header", "Main", "withNoopInstall"], "mappings": ";;;;;;;;;;;AAMY,MAAC,WAAW,GAAGA,mBAAW,CAACC,oBAAS,EAAE;AAClD,SAAEC,gBAAK;AACP,UAAEC,iBAAM;AACR,UAAEC,iBAAM;AACR,QAAEC,eAAI;AACN,CAAC,EAAE;AAES,MAAC,OAAO,GAAGC,uBAAe,CAACJ,gBAAK,EAAE;AAClC,MAAC,QAAQ,GAAGI,uBAAe,CAACH,iBAAM,EAAE;AACpC,MAAC,QAAQ,GAAGG,uBAAe,CAACF,iBAAM,EAAE;AACpC,MAAC,MAAM,GAAGE,uBAAe,CAACD,eAAI;;;;;;;;;"}