{"version": 3, "file": "useResize.js", "sources": ["../../../../../../../packages/components/splitter/src/hooks/useResize.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { getPct, getPx, isPct, isPx } from './useSize'\n\nimport type { ComputedRef, Ref } from 'vue'\nimport type { PanelItemState } from '../type'\n\nexport function useResize(\n  panels: Ref<PanelItemState[]>,\n  containerSize: ComputedRef<number>,\n  pxSizes: ComputedRef<number[]>\n) {\n  const ptg2px = (ptg: number) => ptg * containerSize.value || 0\n\n  function getLimitSize(\n    str: string | number | undefined,\n    defaultLimit: number\n  ) {\n    if (isPct(str)) {\n      return ptg2px(getPct(str))\n    } else if (isPx(str)) {\n      return getPx(str)\n    }\n    return str ?? defaultLimit\n  }\n\n  const movingIndex = ref<{\n    index: number\n    confirmed: boolean\n  } | null>(null)\n\n  let cachePxSizes: number[] = []\n\n  const limitSizes = computed(() =>\n    panels.value.map((item) => [item.min, item.max])\n  )\n\n  const onMoveStart = (index: number) => {\n    movingIndex.value = { index, confirmed: false }\n    cachePxSizes = pxSizes.value\n  }\n\n  const onMoving = (index: number, offset: number) => {\n    let confirmedIndex: number | null = null\n\n    // When overlapping, find the nearest draggable index\n    if ((!movingIndex.value || !movingIndex.value.confirmed) && offset !== 0) {\n      if (offset > 0) {\n        confirmedIndex = index\n        movingIndex.value = { index, confirmed: true }\n      } else {\n        for (let i = index; i >= 0; i -= 1) {\n          if (cachePxSizes[i]! > 0) {\n            confirmedIndex = i\n            movingIndex.value = { index: i, confirmed: true }\n            break\n          }\n        }\n      }\n    }\n    const mergedIndex = confirmedIndex ?? movingIndex.value?.index ?? index\n\n    const numSizes = [...cachePxSizes]\n    const nextIndex = mergedIndex + 1\n\n    // Handle the maximum and minimum edge cases\n    const startMinSize = getLimitSize(limitSizes.value[mergedIndex]![0], 0)\n    const endMinSize = getLimitSize(limitSizes.value[nextIndex]![0], 0)\n    const startMaxSize = getLimitSize(\n      limitSizes.value[mergedIndex]![1],\n      containerSize.value || 0\n    )\n    const endMaxSize = getLimitSize(\n      limitSizes.value[nextIndex]![1],\n      containerSize.value || 0\n    )\n\n    let mergedOffset = offset\n\n    if (numSizes[mergedIndex]! + mergedOffset < startMinSize) {\n      mergedOffset = startMinSize - numSizes[mergedIndex]!\n    }\n    if (numSizes[nextIndex]! - mergedOffset < endMinSize) {\n      mergedOffset = numSizes[nextIndex]! - endMinSize\n    }\n    if (numSizes[mergedIndex]! + mergedOffset > startMaxSize) {\n      mergedOffset = startMaxSize - numSizes[mergedIndex]!\n    }\n    if (numSizes[nextIndex]! - mergedOffset > endMaxSize) {\n      mergedOffset = numSizes[nextIndex]! - endMaxSize\n    }\n\n    numSizes[mergedIndex]! += mergedOffset\n    numSizes[nextIndex]! -= mergedOffset\n\n    panels.value.forEach((panel, index) => {\n      panel.size = numSizes[index]\n    })\n  }\n\n  const onMoveEnd = () => {\n    movingIndex.value = null\n    cachePxSizes = []\n  }\n\n  const cacheCollapsedSize: number[] = []\n  const onCollapse = (index: number, type: 'start' | 'end') => {\n    const currentSizes = pxSizes.value\n\n    const currentIndex = type === 'start' ? index : index + 1\n    const targetIndex = type === 'start' ? index + 1 : index\n\n    const currentSize = currentSizes[currentIndex]!\n    const targetSize = currentSizes[targetIndex]!\n\n    if (currentSize !== 0 && targetSize !== 0) {\n      currentSizes[currentIndex] = 0\n      currentSizes[targetIndex]! += currentSize\n      cacheCollapsedSize[index] = currentSize\n    } else {\n      const totalSize = currentSize + targetSize\n\n      const targetCacheCollapsedSize = cacheCollapsedSize[index]!\n      const currentCacheCollapsedSize = totalSize - targetCacheCollapsedSize\n\n      currentSizes[targetIndex] = targetCacheCollapsedSize\n      currentSizes[currentIndex] = currentCacheCollapsedSize\n    }\n\n    panels.value.forEach((panel, index) => {\n      panel.size = currentSizes[index]\n    })\n  }\n\n  return { onMoveStart, onMoving, onMoveEnd, movingIndex, onCollapse }\n}\n"], "names": ["isPct", "getPct", "isPx", "getPx", "ref", "computed"], "mappings": ";;;;;;;AAEO,SAAS,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE;AAC1D,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;AACzD,EAAE,SAAS,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE;AAC3C,IAAI,IAAIA,aAAK,CAAC,GAAG,CAAC,EAAE;AACpB,MAAM,OAAO,MAAM,CAACC,cAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,KAAK,MAAM,IAAIC,YAAI,CAAC,GAAG,CAAC,EAAE;AAC1B,MAAM,OAAOC,aAAK,CAAC,GAAG,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,YAAY,CAAC;AAC5C,GAAG;AACH,EAAE,MAAM,WAAW,GAAGC,OAAG,CAAC,IAAI,CAAC,CAAC;AAChC,EAAE,IAAI,YAAY,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,UAAU,GAAGC,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtF,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,WAAW,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AACpD,IAAI,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;AACjC,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AACtC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE;AAC9E,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;AACtB,QAAQ,cAAc,GAAG,KAAK,CAAC;AAC/B,QAAQ,WAAW,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AACvD,OAAO,MAAM;AACb,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAC5C,UAAU,IAAI,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACnC,YAAY,cAAc,GAAG,CAAC,CAAC;AAC/B,YAAY,WAAW,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AAC9D,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,CAAC,EAAE,GAAG,cAAc,IAAI,IAAI,GAAG,cAAc,GAAG,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACnJ,IAAI,MAAM,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;AACvC,IAAI,MAAM,SAAS,GAAG,WAAW,GAAG,CAAC,CAAC;AACtC,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvE,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AAClG,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AAC9F,IAAI,IAAI,YAAY,GAAG,MAAM,CAAC;AAC9B,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC,GAAG,YAAY,GAAG,YAAY,EAAE;AAC7D,MAAM,YAAY,GAAG,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,YAAY,GAAG,UAAU,EAAE;AACzD,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;AACtD,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC,GAAG,YAAY,GAAG,YAAY,EAAE;AAC7D,MAAM,YAAY,GAAG,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,YAAY,GAAG,UAAU,EAAE;AACzD,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;AACtD,KAAK;AACL,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC;AAC1C,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC;AACxC,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,KAAK;AAC5C,MAAM,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7B,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAChC,EAAE,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AACtC,IAAI,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;AACvC,IAAI,MAAM,YAAY,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AAC9D,IAAI,MAAM,WAAW,GAAG,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC;AAC7D,IAAI,MAAM,WAAW,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;AACnD,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;AACjD,IAAI,IAAI,WAAW,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE;AAC/C,MAAM,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,YAAY,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC;AAC/C,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,MAAM,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC;AACjD,MAAM,MAAM,wBAAwB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACjE,MAAM,MAAM,yBAAyB,GAAG,SAAS,GAAG,wBAAwB,CAAC;AAC7E,MAAM,YAAY,CAAC,WAAW,CAAC,GAAG,wBAAwB,CAAC;AAC3D,MAAM,YAAY,CAAC,YAAY,CAAC,GAAG,yBAAyB,CAAC;AAC7D,KAAK;AACL,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,KAAK;AAC5C,MAAM,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;AACvE;;;;"}