{"version": 3, "file": "radio-button2.js", "sources": ["../../../../../../packages/components/radio/src/radio-button.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { radioPropsBase } from './radio'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type RadioButton from './radio-button.vue'\n\nexport const radioButtonProps = buildProps({\n  ...radioPropsBase,\n} as const)\n\nexport type RadioButtonProps = ExtractPropTypes<typeof radioButtonProps>\nexport type RadioButtonPropsPublic = __ExtractPublicPropTypes<\n  typeof radioButtonProps\n>\nexport type RadioButtonInstance = InstanceType<typeof RadioButton> & unknown\n"], "names": ["buildProps", "radioPropsBase"], "mappings": ";;;;;;;AAEY,MAAC,gBAAgB,GAAGA,kBAAU,CAAC;AAC3C,EAAE,GAAGC,oBAAc;AACnB,CAAC;;;;"}