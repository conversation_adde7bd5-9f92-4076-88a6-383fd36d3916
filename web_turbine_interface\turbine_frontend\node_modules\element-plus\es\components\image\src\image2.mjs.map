{"version": 3, "file": "image2.mjs", "sources": ["../../../../../../packages/components/image/src/image.vue"], "sourcesContent": ["<template>\n  <div ref=\"container\" v-bind=\"containerAttrs\" :class=\"[ns.b(), $attrs.class]\">\n    <slot v-if=\"hasLoadError\" name=\"error\">\n      <div :class=\"ns.e('error')\">{{ t('el.image.error') }}</div>\n    </slot>\n    <template v-else>\n      <img\n        v-if=\"imageSrc !== undefined\"\n        v-bind=\"imgAttrs\"\n        :src=\"imageSrc\"\n        :loading=\"loading\"\n        :style=\"imageStyle\"\n        :class=\"imageKls\"\n        :crossorigin=\"crossorigin\"\n        @click=\"clickHandler\"\n        @load=\"handleLoad\"\n        @error=\"handleError\"\n      />\n      <div v-if=\"isLoading\" :class=\"ns.e('wrapper')\">\n        <slot name=\"placeholder\">\n          <div :class=\"ns.e('placeholder')\" />\n        </slot>\n      </div>\n    </template>\n    <template v-if=\"preview\">\n      <image-viewer\n        v-if=\"showViewer\"\n        :z-index=\"zIndex\"\n        :initial-index=\"imageIndex\"\n        :infinite=\"infinite\"\n        :zoom-rate=\"zoomRate\"\n        :min-scale=\"minScale\"\n        :max-scale=\"maxScale\"\n        :show-progress=\"showProgress\"\n        :url-list=\"previewSrcList\"\n        :crossorigin=\"crossorigin\"\n        :hide-on-click-modal=\"hideOnClickModal\"\n        :teleported=\"previewTeleported\"\n        :close-on-press-escape=\"closeOnPressEscape\"\n        @close=\"closeViewer\"\n        @switch=\"switchViewer\"\n      >\n        <div v-if=\"$slots.viewer\">\n          <slot name=\"viewer\" />\n        </div>\n        <template v-if=\"$slots.progress\" #progress=\"progress\">\n          <slot name=\"progress\" v-bind=\"progress\" />\n        </template>\n        <template #toolbar=\"toolbar\">\n          <slot name=\"toolbar\" v-bind=\"toolbar\" />\n        </template>\n      </image-viewer>\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  ref,\n  useAttrs as useRawAttrs,\n  watch,\n} from 'vue'\nimport { useEventListener, useThrottleFn } from '@vueuse/core'\nimport { fromPairs } from 'lodash-unified'\nimport { useAttrs, useLocale, useNamespace } from '@element-plus/hooks'\nimport ImageViewer from '@element-plus/components/image-viewer'\nimport {\n  getScrollContainer,\n  isArray,\n  isClient,\n  isElement,\n  isInContainer,\n  isString,\n} from '@element-plus/utils'\nimport { imageEmits, imageProps } from './image'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElImage',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(imageProps)\nconst emit = defineEmits(imageEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('image')\nconst rawAttrs = useRawAttrs()\n\nconst containerAttrs = computed(() => {\n  return fromPairs(\n    Object.entries(rawAttrs).filter(\n      ([key]) => /^(data-|on[A-Z])/i.test(key) || ['id', 'style'].includes(key)\n    )\n  )\n})\n\nconst imgAttrs = useAttrs({\n  excludeListeners: true,\n  excludeKeys: computed<string[]>(() => {\n    return Object.keys(containerAttrs.value)\n  }),\n})\n\nconst imageSrc = ref<string | undefined>()\nconst hasLoadError = ref(false)\nconst isLoading = ref(true)\nconst showViewer = ref(false)\nconst container = ref<HTMLElement>()\nconst _scrollContainer = ref<HTMLElement | Window>()\n\nconst supportLoading = isClient && 'loading' in HTMLImageElement.prototype\nlet stopScrollListener: (() => void) | undefined\n\nconst imageKls = computed(() => [\n  ns.e('inner'),\n  preview.value && ns.e('preview'),\n  isLoading.value && ns.is('loading'),\n])\n\nconst imageStyle = computed<CSSProperties>(() => {\n  const { fit } = props\n  if (isClient && fit) {\n    return { objectFit: fit }\n  }\n  return {}\n})\n\nconst preview = computed(() => {\n  const { previewSrcList } = props\n  return isArray(previewSrcList) && previewSrcList.length > 0\n})\n\nconst imageIndex = computed(() => {\n  const { previewSrcList, initialIndex } = props\n  let previewIndex = initialIndex\n  if (initialIndex > previewSrcList.length - 1) {\n    previewIndex = 0\n  }\n  return previewIndex\n})\n\nconst isManual = computed(() => {\n  if (props.loading === 'eager') return false\n  return (!supportLoading && props.loading === 'lazy') || props.lazy\n})\n\nconst loadImage = () => {\n  if (!isClient) return\n\n  // reset status\n  isLoading.value = true\n  hasLoadError.value = false\n  imageSrc.value = props.src\n}\n\nfunction handleLoad(event: Event) {\n  isLoading.value = false\n  hasLoadError.value = false\n  emit('load', event)\n}\n\nfunction handleError(event: Event) {\n  isLoading.value = false\n  hasLoadError.value = true\n  emit('error', event)\n}\n\nfunction handleLazyLoad() {\n  if (isInContainer(container.value, _scrollContainer.value)) {\n    loadImage()\n    removeLazyLoadListener()\n  }\n}\n\nconst lazyLoadHandler = useThrottleFn(handleLazyLoad, 200, true)\n\nasync function addLazyLoadListener() {\n  if (!isClient) return\n\n  await nextTick()\n\n  const { scrollContainer } = props\n  if (isElement(scrollContainer)) {\n    _scrollContainer.value = scrollContainer\n  } else if (isString(scrollContainer) && scrollContainer !== '') {\n    _scrollContainer.value =\n      document.querySelector<HTMLElement>(scrollContainer) ?? undefined\n  } else if (container.value) {\n    _scrollContainer.value = getScrollContainer(container.value)\n  }\n\n  if (_scrollContainer.value) {\n    stopScrollListener = useEventListener(\n      _scrollContainer,\n      'scroll',\n      lazyLoadHandler\n    )\n    setTimeout(() => handleLazyLoad(), 100)\n  }\n}\n\nfunction removeLazyLoadListener() {\n  if (!isClient || !_scrollContainer.value || !lazyLoadHandler) return\n\n  stopScrollListener?.()\n  _scrollContainer.value = undefined\n}\n\nfunction clickHandler() {\n  // don't show viewer when preview is false\n  if (!preview.value) return\n  showViewer.value = true\n  emit('show')\n}\n\nfunction closeViewer() {\n  showViewer.value = false\n  emit('close')\n}\n\nfunction switchViewer(val: number) {\n  emit('switch', val)\n}\n\nwatch(\n  () => props.src,\n  () => {\n    if (isManual.value) {\n      // reset status\n      isLoading.value = true\n      hasLoadError.value = false\n      removeLazyLoadListener()\n      addLazyLoadListener()\n    } else {\n      loadImage()\n    }\n  }\n)\n\nonMounted(() => {\n  if (isManual.value) {\n    addLazyLoadListener()\n  } else {\n    loadImage()\n  }\n})\n\ndefineExpose({\n  /** @description manually open preview */\n  showPreview: clickHandler,\n})\n</script>\n"], "names": ["useRawAttrs", "useAttrs", "_openBlock", "_createElementBlock", "_mergeProps", "_unref"], "mappings": ";;;;;;;;;;;;;;mCAiFc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAA,MAAM,WAAWA,QAAY,EAAA,CAAA;AAE7B,IAAM,MAAA,cAAA,GAAiB,SAAS,MAAM;AACpC,MAAO,OAAA,SAAA,CAAA,MAAA,CAAA,OAAA,CAAA,QAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,KAAA,mBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAAA,KACL,CAAA,CAAA;AAAyB,IAAA,MACvB,QAAK,GAAMC,UAAoB,CAAA;AAAyC,MAC1E,gBAAA,EAAA,IAAA;AAAA,MACF,WAAA,EAAA,QAAA,CAAA,MAAA;AAAA,QACD,OAAA,MAAA,CAAA,IAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAED,OAAA,CAAA;AAA0B,KAAA,CACxB,CAAkB;AAAA,IAClB,MAAA,QAAA;AACE,IAAO,MAAA,YAAO,GAAK,GAAA,CAAA,KAAA,CAAA,CAAA;AAAoB,IAAA,MACxC,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AAAA,IACH,MAAC,UAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAM,YAAmC,GAAA,EAAA,CAAA;AACzC,IAAM,MAAA,mBAAmB,GAAK,EAAA,CAAA;AAC9B,IAAM,MAAA,iBAAoB,QAAA,IAAA,SAAA,IAAA,gBAAA,CAAA,SAAA,CAAA;AAC1B,IAAM,IAAA,mBAAiB;AACvB,IAAA,MAAM,mBAA6B,CAAA,MAAA;AACnC,MAAA,EAAA,CAAA,CAAM;AAEN,MAAM,OAAA,CAAA,KAAA,IAAA,EAAA,CAAA,CAAiB,CAAY,SAAA,CAAA;AACnC,MAAI,SAAA,CAAA,KAAA,IAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA;AAEJ,KAAM,CAAA,CAAA;AAA0B,IAC9B,gBAAY,GAAA,QAAA,CAAA,MAAA;AAAA,MACZ,MAAQ,EAAA,GAAA,EAAA,GAAA,KAAY,CAAE;AAAS,MAC/B,IAAU,QAAA,IAAA,GAAS,EAAG;AAAY,QACnC,OAAA,EAAA,SAAA,EAAA,GAAA,EAAA,CAAA;AAED,OAAM;AACJ,MAAM,OAAA;AACN,KAAA,CAAA,CAAA;AACE,IAAO,MAAA,OAAE,WAAW,CAAI,MAAA;AAAA,MAC1B,MAAA,EAAA,cAAA,EAAA,GAAA,KAAA,CAAA;AACA,MAAA,OAAO,OAAC,CAAA,cAAA,CAAA,IAAA,cAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AAAA,KACT,CAAA,CAAA;AAED,IAAM,MAAA,UAAU,WAAe,CAAA,MAAA;AAC7B,MAAM,MAAA,EAAE,gBAAmB,YAAA,EAAA,GAAA,KAAA,CAAA;AAC3B,MAAA,IAAA,YAAe,GAAA,YAAmB,CAAA;AAAwB,MAC3D,IAAA,YAAA,GAAA,cAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAED,QAAM,YAAA,GAAa;AACjB,OAAM;AACN,MAAA,OAAmB,YAAA,CAAA;AACnB,KAAI,CAAA,CAAA;AACF,IAAe,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACjB,IAAA,KAAA,CAAA,OAAA,KAAA,OAAA;AACA,QAAO,OAAA,KAAA,CAAA;AAAA,MACR,OAAA,CAAA,cAAA,IAAA,KAAA,CAAA,OAAA,KAAA,MAAA,IAAA,KAAA,CAAA,IAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,SAAkB,GAAA,MAAA;AACtB,MAAA,IAAA,CAAA,QAAS;AAAqD,QAC/D,OAAA;AAED,MAAA,kBAAwB,IAAA,CAAA;AACtB,MAAA,YAAe,CAAA,KAAA,GAAA,KAAA,CAAA;AAGf,MAAA,QAAA,CAAA,KAAkB,GAAA,KAAA,CAAA,GAAA,CAAA;AAClB,KAAA,CAAA;AACA,IAAA,SAAA,UAAiB,CAAM,KAAA,EAAA;AAAA,MACzB,SAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,kBAAoB,GAAc,KAAA,CAAA;AAChC,MAAA,IAAA,CAAA,MAAkB,EAAA,KAAA,CAAA,CAAA;AAClB,KAAA;AACA,IAAA,oBAAkB,CAAA,KAAA,EAAA;AAAA,MACpB,SAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,qBAAmC,IAAA,CAAA;AACjC,MAAA,IAAA,CAAA,OAAkB,EAAA,KAAA,CAAA,CAAA;AAClB,KAAA;AACA,IAAA,uBAAmB,GAAA;AAAA,MACrB,IAAA,aAAA,CAAA,SAAA,CAAA,KAAA,EAAA,gBAAA,CAAA,KAAA,CAAA,EAAA;AAEA,QAAA,SAA0B,EAAA,CAAA;AACxB,QAAA,sBAAkB,EAAA,CAAA;AAChB,OAAU;AACV,KAAuB;AAAA,IACzB,MAAA,eAAA,GAAA,aAAA,CAAA,cAAA,EAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IACF,eAAA,mBAAA,GAAA;AAEA,MAAA,IAAM,EAAkB,CAAA;AAExB,MAAA,IAAA,CAAA,QAAe;AACb,QAAA,OAAe;AAEf,MAAA,MAAM,QAAS,EAAA,CAAA;AAEf,MAAM,MAAA,EAAE,iBAAoB,GAAA,KAAA,CAAA;AAC5B,MAAI,IAAA,SAAA,CAAU,eAAe,CAAG,EAAA;AAC9B,QAAA,gBAAA,CAAiB,KAAQ,GAAA,eAAA,CAAA;AAAA,OAChB,MAAA,IAAA,QAAA,CAAS,eAAe,CAAA,IAAK,oBAAoB,EAAI,EAAA;AAC9D,QAAA,gBAAA,CAAiB,KACf,GAAA,CAAA,EAAA,GAAA,QAAoC,CAAA,aAAA,CAAA,eAAoB,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,CAAA;AAAA,OAC5D,MAAA,IAAW,UAAU,KAAO,EAAA;AAC1B,QAAiB,gBAAA,CAAA,KAAA,GAAQ,kBAAmB,CAAA,SAAA,CAAU,KAAK,CAAA,CAAA;AAAA,OAC7D;AAEA,MAAA,IAAI,iBAAiB,KAAO,EAAA;AAC1B,QAAqB,kBAAA,GAAA,gBAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,eAAA,CAAA,CAAA;AAAA,QACnB,UAAA,CAAA,MAAA,cAAA,EAAA,EAAA,GAAA,CAAA,CAAA;AAAA,OACA;AAAA,KACA;AAAA,IACF,SAAA,sBAAA,GAAA;AACA,MAAW,IAAA,CAAA,QAAA,IAAA,CAAA,gBAAqB,CAAA,KAAM,IAAA,CAAA,eAAA;AAAA,QACxC,OAAA;AAAA,MACF,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AAEA,MAAA,gBAAkC,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAChC,KAAA;AAEA,IAAqB,SAAA,YAAA,GAAA;AACrB,MAAA,IAAA,CAAA,OAAA,CAAA,KAAyB;AAAA,QAC3B,OAAA;AAEA,MAAA,UAAwB,CAAA,KAAA,GAAA,IAAA,CAAA;AAEtB,MAAI,IAAA,CAAC,QAAQ;AACb,KAAA;AACA,IAAA,SAAW,WAAA,GAAA;AAAA,MACb,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAA,CAAA,OAAuB,CAAA,CAAA;AACrB,KAAA;AACA,IAAA,SAAY,YAAA,CAAA,GAAA,EAAA;AAAA,MACd,IAAA,CAAA,QAAA,EAAA,GAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,KAAA,CAAA,WAAe,CAAG,GAAA,EAAA,MAAA;AAAA,MACpB,IAAA,QAAA,CAAA,KAAA,EAAA;AAEA,QAAA,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,oBACc,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,QACN,sBAAA,EAAA,CAAA;AACJ,QAAA,mBAAoB,EAAA,CAAA;AAElB,OAAA,MAAA;AACA,QAAA,SAAA,EAAA,CAAA;AACA,OAAuB;AACvB,KAAoB,CAAA,CAAA;AAAA,IAAA,SACf,CAAA,MAAA;AACL,MAAU,IAAA,QAAA,CAAA,KAAA,EAAA;AAAA,QACZ,mBAAA,EAAA,CAAA;AAAA,OACF,MAAA;AAAA,QACF,SAAA,EAAA,CAAA;AAEA,OAAA;AACE,KAAA,CAAA,CAAA;AACE,IAAoB,MAAA,CAAA;AAAA,MACtB,WAAO,EAAA,YAAA;AACL,KAAU,CAAA,CAAA;AAAA,IACZ,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAAC,UAAA,CAAA;AAED,QAAa,OAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA,SAAA;AAAA,OAEE,EAAAC,KAAA,CAAA,cAAA,CAAA,EAAA;AAAA,QACd,KAAA,EAAA,CAAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}