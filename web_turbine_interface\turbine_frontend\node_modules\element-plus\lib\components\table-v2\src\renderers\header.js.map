{"version": 3, "file": "header.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/header.tsx"], "sourcesContent": ["import { HeaderRow } from '../components'\nimport { tryCall } from '../utils'\n\nimport type { FunctionalComponent } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { TableV2HeaderRendererParams } from '../components'\nimport type { TableV2Props } from '../table'\n\ntype HeaderRendererProps = TableV2HeaderRendererParams &\n  Pick<TableV2Props, 'headerClass' | 'headerProps'> & {\n    ns: UseNamespaceReturn\n  }\n\nconst HeaderRenderer: FunctionalComponent<HeaderRendererProps> = (\n  {\n    columns,\n    columnsStyles,\n    headerIndex,\n    style,\n    // derived from root\n    headerClass,\n    headerProps,\n\n    ns,\n  },\n  { slots }\n) => {\n  const param = { columns, headerIndex }\n\n  const kls = [\n    ns.e('header-row'),\n    tryCall(headerClass, param, ''),\n    {\n      // [ns.is('resizing')]: <PERSON>ole<PERSON>(resizingKey),\n      [ns.is('customized')]: <PERSON><PERSON>an(slots.header),\n    },\n  ]\n\n  const extraProps = {\n    ...tryCall(headerProps, param),\n    columnsStyles,\n    class: kls,\n    columns,\n    headerIndex,\n    style,\n  }\n\n  return <HeaderRow {...extraProps}>{slots}</HeaderRow>\n}\n\nexport default HeaderRenderer\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "columns", "columnsStyles", "headerIndex", "style", "headerProps", "ns", "slots", "tryCall", "kls", "e", "extraProps", "class", "_createVNode", "HeaderRow"], "mappings": ";;;;;;;;;;;;;AAaA,EAAMA,aAAAA;EAEFC,WADF;EAEEC,KAFF;EAGEC,WAHF;EAIEC,WAJF;AAKE,EAAA,EAAA;GALF;EAOEC,KAPF;AASEC,CAAAA,KAAAA;AATF,EAWA,MAAA,KAAA,GAAA;AAAEC,IAAAA,OAAAA;AAAF,IACG,WAAA;AACH,GAAA,CAAA;QAAc,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,EAAAC,aAAA,CAAA,WAAA,EAAA,KAAA,EAAA,EAAA,CAAA,EAAA;AAAWL,IAAAA,CAAAA,EAAAA,CAAAA,EAAAA,CAAAA,YAAAA,CAAAA,GAAAA,OAAAA,CAAAA,KAAAA,CAAAA,MAAAA,CAAAA;GAAzB,CAAA,CAAA;AAEA,EAAA,MAAMM,UACDC,GAAH;AAGE,IAAA,GAAAF,aAAA,CAAA,WAAA,EAAA,KAAA,CAAA;IACA,aAAO;AAFT,IAHF,KAAA,EAAA,GAAA;IASMG,OAAAA;IAEJT,WAFiB;AAGjBU,IAAAA,KAAK;IACLX;SAJiBY,eAAA,CAAAC,oBAAA,EAAA,UAAA,EAAA,OAAA,CAAA,KAAA,CAAA,GAAA,KAAA,GAAA;AAMjBV,IAAAA,OAAAA,EAAAA,MAAAA,CAAAA,KAAAA,CAAAA;GANF,CAAA,CAAA;AASA,CAAA,CAAA;AAAA,aAAA,cAAA;;;;"}