{"version": 3, "file": "defaults.js", "sources": ["../../../packages/element-plus/defaults.ts"], "sourcesContent": ["import { makeInstaller } from './make-installer'\nimport Components from './component'\nimport Plugins from './plugin'\n\nexport default makeInstaller([...Components, ...Plugins])\n"], "names": ["makeInstaller", "Components", "Plugins"], "mappings": ";;;;;;;;AAGA,gBAAeA,2BAAa,CAAC,CAAC,GAAGC,oBAAU,EAAE,GAAGC,iBAAO,CAAC,CAAC;;;;"}