{"version": 3, "file": "sv.js", "sources": ["../../../../../packages/locale/lang/sv.ts"], "sourcesContent": ["export default {\n  name: 'sv',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Töm',\n    },\n    datepicker: {\n      now: 'Nu',\n      today: 'Idag',\n      cancel: 'Av<PERSON><PERSON>t',\n      clear: 'Töm',\n      confirm: 'OK',\n      selectDate: 'Välj datum',\n      selectTime: 'Välj tid',\n      startDate: 'Startdatum',\n      startTime: 'Starttid',\n      endDate: 'Slutdatum',\n      endTime: 'Sluttid',\n      prevYear: 'Föregående år',\n      nextYear: 'Nästa år',\n      prevMonth: 'Föregående månad',\n      nextMonth: 'Nästa månad',\n      year: '',\n      month1: 'Januari',\n      month2: 'Februari',\n      month3: 'Mars',\n      month4: 'April',\n      month5: 'Maj',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'Augusti',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'December',\n      // week: 'week',\n      weeks: {\n        sun: '<PERSON>ön',\n        mon: '<PERSON>å<PERSON>',\n        tue: 'Tis',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lör',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Laddar',\n      noMatch: 'Hittade inget',\n      noData: 'Ingen data',\n      placeholder: 'Välj',\n    },\n    mention: {\n      loading: 'Laddar',\n    },\n    cascader: {\n      noMatch: 'Hittade inget',\n      loading: 'Laddar',\n      placeholder: 'Välj',\n      noData: 'Ingen data',\n    },\n    pagination: {\n      goto: 'Gå till',\n      pagesize: '/sida',\n      total: 'Totalt {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Meddelande',\n      confirm: 'OK',\n      cancel: 'Avbryt',\n      error: 'Felaktig inmatning',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Radera',\n      preview: 'Förhandsvisa',\n      continue: 'Fortsätt',\n    },\n    table: {\n      emptyText: 'Inga Data',\n      confirmFilter: 'Bekräfta',\n      resetFilter: 'Återställ',\n      clearFilter: 'Alla',\n      sumText: 'Summa',\n    },\n    tour: {\n      next: 'Nästa',\n      previous: 'Föregående',\n      finish: 'Avsluta',\n    },\n    tree: {\n      emptyText: 'Ingen data',\n    },\n    transfer: {\n      noMatch: 'Hittade inget',\n      noData: 'Ingen data',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'Enter keyword', // to be translated\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Bakåt', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nej',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,IAAI;AACf,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,QAAQ,EAAE,wBAAwB;AACxC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,2BAA2B;AAC5C,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,QAAQ,EAAE,aAAa;AAC7B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,aAAa,EAAE,aAAa;AAClC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,MAAM,EAAE,SAAS;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAClC,MAAM,iBAAiB,EAAE,eAAe;AACxC,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,UAAU;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}