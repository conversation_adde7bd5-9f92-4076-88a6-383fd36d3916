{"version": 3, "file": "bn.min.js", "sources": ["../../../../packages/locale/lang/bn.ts"], "sourcesContent": ["export default {\n  name: 'bn',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'ঠিক আছে',\n      clear: 'ক্লিয়ার',\n    },\n    datepicker: {\n      now: 'এখন',\n      today: 'আজ',\n      cancel: 'বাতিল',\n      clear: 'ক্লিয়ার',\n      confirm: 'ঠিক আছে',\n      selectDate: 'তারিখ নির্বাচন করুন',\n      selectTime: 'সময় নির্বাচন করুন',\n      startDate: 'যে তারিখ থেকে',\n      startTime: 'যে সময় থেকে',\n      endDate: 'যে তারিখ পর্যন্ত',\n      endTime: 'যে সময় পর্যন্ত',\n      prevYear: 'পূর্ববর্তী বছর',\n      nextYear: 'পরবর্তী বছর',\n      prevMonth: 'পূর্ববর্তী মাস',\n      nextMonth: 'পরবর্তী মাস',\n      year: 'সাল',\n      month1: 'জানুয়ারি',\n      month2: 'ফেব্রুয়ারী',\n      month3: 'মার্চ',\n      month4: 'এপ্রিল',\n      month5: 'মে',\n      month6: 'জুন',\n      month7: 'জুলাই',\n      month8: 'আগষ্ট',\n      month9: 'সেপ্টেম্বর',\n      month10: 'অক্টোবর',\n      month11: 'নভেম্বর',\n      month12: 'ডিসেম্বর',\n      week: 'সাপ্তাহ',\n      weeks: {\n        sun: 'রবি',\n        mon: 'সোম',\n        tue: 'মঙ্গল',\n        wed: 'বুধ',\n        thu: 'বৃহঃ',\n        fri: 'শুক্র',\n        sat: 'শনি',\n      },\n      months: {\n        jan: 'জানু',\n        feb: 'ফেব্রু',\n        mar: 'মার্চ',\n        apr: 'এপ্রি',\n        may: 'মে',\n        jun: 'জুন',\n        jul: 'জুলা',\n        aug: 'আগ',\n        sep: 'সেপ্টে',\n        oct: 'আক্টো',\n        nov: 'নভে',\n        dec: 'ডিসে',\n      },\n    },\n    select: {\n      loading: 'লোড হচ্ছে',\n      noMatch: 'কোন মিল পওয়া যায়নি',\n      noData: 'কোন ডাটা নেই',\n      placeholder: 'নির্বাচন করুন',\n    },\n    mention: {\n      loading: 'লোড হচ্ছে',\n    },\n    cascader: {\n      noMatch: 'কোন মিল পওয়া যায়নি',\n      loading: 'লোড হচ্ছে',\n      placeholder: 'নির্বাচন করুন',\n      noData: 'কোন ডাটা নেই',\n    },\n    pagination: {\n      goto: 'যান',\n      pagesize: '/পেজ',\n      total: 'মোট {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'অপ্রচলিত (Deprecated) ব্যাবহার পওয়া গেছে, আরও জানতে চাইলে, দয়া করে el-pagination এর ডকুমেন্টেশন দেখুন',\n    },\n    messagebox: {\n      title: 'বার্তা',\n      confirm: 'ঠিক আছে',\n      cancel: 'বাতিল',\n      error: 'ইনপুট ডাটা গ্রহনযোগ্য নয়',\n    },\n    upload: {\n      deleteTip: 'অপসারণ করতে \"ডিলিট\" এ ক্লিক করুন',\n      delete: 'ডিলিট',\n      preview: 'প্রিভিউ',\n      continue: 'চালিয়ে যান',\n    },\n    table: {\n      emptyText: 'কোন ডাটা নেই',\n      confirmFilter: 'নিশ্চিত করুন',\n      resetFilter: 'রিসেট',\n      clearFilter: 'সব',\n      sumText: 'সারাংশ',\n    },\n    tree: {\n      emptyText: 'কোন ডাটা নেই',\n    },\n    transfer: {\n      noMatch: 'কোন মিল পওয়া যায়নি',\n      noData: 'কোন ডাটা নেই',\n      titles: ['লিস্ট ১', 'লিস্ট ২'],\n      filterPlaceholder: 'সার্চ করুন',\n      noCheckedFormat: '{total} আইটেম',\n      hasCheckedFormat: '{checked}/{total} টিক করা হয়েছে',\n    },\n    image: {\n      error: 'ব্যর্থ হয়েছে',\n    },\n    pageHeader: {\n      title: 'পিছনে',\n    },\n    popconfirm: {\n      confirmButtonText: 'হ্যা',\n      cancelButtonText: 'না',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,uCAAuC,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,4CAA4C,CAAC,OAAO,CAAC,uCAAuC,CAAC,UAAU,CAAC,0GAA0G,CAAC,UAAU,CAAC,8FAA8F,CAAC,SAAS,CAAC,sEAAsE,CAAC,SAAS,CAAC,0DAA0D,CAAC,OAAO,CAAC,wFAAwF,CAAC,OAAO,CAAC,4EAA4E,CAAC,QAAQ,CAAC,iFAAiF,CAAC,QAAQ,CAAC,+DAA+D,CAAC,SAAS,CAAC,iFAAiF,CAAC,SAAS,CAAC,+DAA+D,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,8DAA8D,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,8DAA8D,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,kDAAkD,CAAC,IAAI,CAAC,4CAA4C,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,OAAO,CAAC,+FAA+F,CAAC,MAAM,CAAC,gEAAgE,CAAC,WAAW,CAAC,2EAA2E,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+FAA+F,CAAC,OAAO,CAAC,mDAAmD,CAAC,WAAW,CAAC,2EAA2E,CAAC,MAAM,CAAC,gEAAgE,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,CAAC,4BAA4B,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,wZAAwZ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,uCAAuC,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,mIAAmI,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+JAA+J,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,4CAA4C,CAAC,QAAQ,CAAC,yDAAyD,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gEAAgE,CAAC,aAAa,CAAC,qEAAqE,CAAC,WAAW,CAAC,gCAAgC,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gEAAgE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+FAA+F,CAAC,MAAM,CAAC,gEAAgE,CAAC,MAAM,CAAC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,CAAC,iBAAiB,CAAC,yDAAyD,CAAC,eAAe,CAAC,wCAAwC,CAAC,gBAAgB,CAAC,wFAAwF,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}