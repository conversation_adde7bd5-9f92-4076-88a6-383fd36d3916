{"version": 3, "file": "utils.js", "sources": ["../../../../../../../packages/components/table-v2/src/composables/utils.ts"], "sourcesContent": ["import type { CSSProperties } from 'vue'\nimport type { AnyColumns } from '../types'\n\nexport const calcColumnStyle = (\n  column: AnyColumns[number],\n  fixedColumn: boolean,\n  fixed: boolean\n): CSSProperties => {\n  const flex = {\n    flexGrow: 0,\n    flexShrink: 0,\n    ...(fixed\n      ? {}\n      : {\n          flexGrow: column.flexGrow || 0,\n          flexShrink: column.flexShrink || 1,\n        }),\n  }\n\n  if (!fixed) {\n    flex.flexShrink = 1\n  }\n\n  const style = {\n    ...(column.style ?? {}),\n    ...flex,\n    flexBasis: 'auto',\n    width: column.width,\n  }\n\n  if (!fixedColumn) {\n    if (column.maxWidth) style.maxWidth = column.maxWidth\n    if (column.minWidth) style.minWidth = column.minWidth\n  }\n\n  return style\n}\n"], "names": [], "mappings": ";;;;AAAY,MAAC,eAAe,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,KAAK;AAC/D,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,IAAI,GAAG;AACf,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;AACpC,MAAM,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;AACxC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,GAAG;AACH,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AAC5C,IAAI,GAAG,IAAI;AACX,IAAI,SAAS,EAAE,MAAM;AACrB,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK;AACvB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,IAAI,MAAM,CAAC,QAAQ;AACvB,MAAM,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACvC,IAAI,IAAI,MAAM,CAAC,QAAQ;AACvB,MAAM,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf;;;;"}