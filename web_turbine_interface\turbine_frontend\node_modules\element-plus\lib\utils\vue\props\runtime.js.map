{"version": 3, "file": "runtime.js", "sources": ["../../../../../../packages/utils/vue/props/runtime.ts"], "sourcesContent": ["import { warn } from 'vue'\nimport { fromPairs } from 'lodash-unified'\nimport { isObject } from '../../types'\nimport { hasOwn } from '../../objects'\n\nimport type { PropType } from 'vue'\nimport type {\n  EpProp,\n  EpPropConvert,\n  EpPropFinalized,\n  EpPropInput,\n  EpPropMergeType,\n  IfEpProp,\n  IfNativePropType,\n  NativePropType,\n} from './types'\n\nexport const epPropKey = '__epPropKey'\n\nexport const definePropType = <T>(val: any): PropType<T> => val\n\nexport const isEpProp = (val: unknown): val is EpProp<any, any, any> =>\n  isObject(val) && !!(val as any)[epPropKey]\n\n/**\n * @description Build prop. It can better optimize prop types\n * @description 生成 prop，能更好地优化类型\n * @example\n  // limited options\n  // the type will be PropType<'light' | 'dark'>\n  buildProp({\n    type: String,\n    values: ['light', 'dark'],\n  } as const)\n  * @example\n  // limited options and other types\n  // the type will be PropType<'small' | 'large' | number>\n  buildProp({\n    type: [String, Number],\n    values: ['small', 'large'],\n    validator: (val: unknown): val is number => typeof val === 'number',\n  } as const)\n  @link see more: https://github.com/element-plus/element-plus/pull/3341\n */\nexport const buildProp = <\n  Type = never,\n  Value = never,\n  Validator = never,\n  Default extends EpPropMergeType<Type, Value, Validator> = never,\n  Required extends boolean = false\n>(\n  prop: EpPropInput<Type, Value, Validator, Default, Required>,\n  key?: string\n): EpPropFinalized<Type, Value, Validator, Default, Required> => {\n  // filter native prop type and nested prop, e.g `null`, `undefined` (from `buildProps`)\n  if (!isObject(prop) || isEpProp(prop)) return prop as any\n\n  const { values, required, default: defaultValue, type, validator } = prop\n\n  const _validator =\n    values || validator\n      ? (val: unknown) => {\n          let valid = false\n          let allowedValues: unknown[] = []\n\n          if (values) {\n            allowedValues = Array.from(values)\n            if (hasOwn(prop, 'default')) {\n              allowedValues.push(defaultValue)\n            }\n            valid ||= allowedValues.includes(val)\n          }\n          if (validator) valid ||= validator(val)\n\n          if (!valid && allowedValues.length > 0) {\n            const allowValuesText = [...new Set(allowedValues)]\n              .map((value) => JSON.stringify(value))\n              .join(', ')\n            warn(\n              `Invalid prop: validation failed${\n                key ? ` for prop \"${key}\"` : ''\n              }. Expected one of [${allowValuesText}], got value ${JSON.stringify(\n                val\n              )}.`\n            )\n          }\n          return valid\n        }\n      : undefined\n\n  const epProp: any = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true,\n  }\n  if (hasOwn(prop, 'default')) epProp.default = defaultValue\n  return epProp\n}\n\nexport const buildProps = <\n  Props extends Record<\n    string,\n    | { [epPropKey]: true }\n    | NativePropType\n    | EpPropInput<any, any, any, any, any>\n  >\n>(\n  props: Props\n): {\n  [K in keyof Props]: IfEpProp<\n    Props[K],\n    Props[K],\n    IfNativePropType<Props[K], Props[K], EpPropConvert<Props[K]>>\n  >\n} =>\n  fromPairs(\n    Object.entries(props).map(([key, option]) => [\n      key,\n      buildProp(option as any, key),\n    ])\n  ) as any\n"], "names": ["isObject", "hasOwn", "warn", "fromPairs"], "mappings": ";;;;;;;;AAIY,MAAC,SAAS,GAAG,cAAc;AAC3B,MAAC,cAAc,GAAG,CAAC,GAAG,KAAK,IAAI;AAC/B,MAAC,QAAQ,GAAG,CAAC,GAAG,KAAKA,eAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE;AACvD,MAAC,SAAS,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK;AACxC,EAAE,IAAI,CAACA,eAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC;AACvC,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAC5E,EAAE,MAAM,UAAU,GAAG,MAAM,IAAI,SAAS,GAAG,CAAC,GAAG,KAAK;AACpD,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC;AACtB,IAAI,IAAI,aAAa,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC,MAAM,IAAIC,aAAM,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;AACnC,QAAQ,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACzC,OAAO;AACP,MAAM,KAAK,KAAK,KAAK,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,SAAS;AACjB,MAAM,KAAK,KAAK,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,KAAK,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,MAAM,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3G,MAAMC,QAAI,CAAC,CAAC,+BAA+B,EAAE,GAAG,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,mBAAmB,EAAE,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzJ,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,GAAG,KAAK,CAAC,CAAC;AACb,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,IAAI;AACR,IAAI,QAAQ,EAAE,CAAC,CAAC,QAAQ;AACxB,IAAI,SAAS,EAAE,UAAU;AACzB,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,GAAG,CAAC;AACJ,EAAE,IAAID,aAAM,CAAC,IAAI,EAAE,SAAS,CAAC;AAC7B,IAAI,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC;AAClC,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,UAAU,GAAG,CAAC,KAAK,KAAKE,uBAAS,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK;AAC5F,EAAE,GAAG;AACL,EAAE,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC;AACxB,CAAC,CAAC;;;;;;;;"}