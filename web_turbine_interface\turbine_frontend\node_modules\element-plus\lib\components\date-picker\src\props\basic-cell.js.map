{"version": 3, "file": "basic-cell.js", "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-cell.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { DateCell } from '../date-picker.type'\n\nexport const basicCellProps = buildProps({\n  cell: {\n    type: definePropType<DateCell>(Object),\n  },\n} as const)\n\nexport type BasicCellProps = ExtractPropTypes<typeof basicCellProps>\nexport type BasicCellPropsPublic = __ExtractPublicPropTypes<\n  typeof basicCellProps\n>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,cAAc,GAAGA,kBAAU,CAAC;AACzC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC;;;;"}