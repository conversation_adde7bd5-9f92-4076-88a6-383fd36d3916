{"version": 3, "file": "statistic.js", "sources": ["../../../../../../packages/components/statistic/src/statistic.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type {\n  ExtractPropTypes,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type Statistic from './statistic.vue'\n\nexport const statisticProps = buildProps({\n  /**\n   * @description Setting the decimal point\n   */\n  decimalSeparator: {\n    type: String,\n    default: '.',\n  },\n  /**\n   * @description Sets the thousandth identifier\n   */\n  groupSeparator: {\n    type: String,\n    default: ',',\n  },\n  /**\n   * @description numerical precision\n   */\n  precision: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description Custom numerical presentation\n   */\n  formatter: Function,\n  /**\n   * @description Numerical content\n   */\n  value: {\n    type: definePropType<number | Dayjs>([Number, Object]),\n    default: 0,\n  },\n  /**\n   * @description Sets the prefix of a number\n   */\n  prefix: String,\n\n  /**\n   * @description  Sets the suffix of a number\n   */\n  suffix: String,\n  /**\n   * @description Numeric titles\n   */\n  title: String,\n  /**\n   * @description Styles numeric values\n   */\n  valueStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n  },\n} as const)\nexport type StatisticProps = ExtractPropTypes<typeof statisticProps>\nexport type StatisticPropsPublic = __ExtractPublicPropTypes<\n  typeof statisticProps\n>\n\nexport type StatisticInstance = InstanceType<typeof Statistic> & unknown\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,cAAc,GAAGA,kBAAU,CAAC;AACzC,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG;AACH,CAAC;;;;"}