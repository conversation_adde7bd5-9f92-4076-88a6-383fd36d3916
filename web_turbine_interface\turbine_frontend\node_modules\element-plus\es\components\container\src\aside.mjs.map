{"version": 3, "file": "aside.mjs", "sources": ["../../../../../../packages/components/container/src/aside.vue"], "sourcesContent": ["<template>\n  <aside :class=\"ns.b()\" :style=\"style\">\n    <slot />\n  </aside>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElAside',\n})\nconst props = defineProps({\n  /**\n   * @description width of the side section\n   */\n  width: {\n    type: String,\n    default: null,\n  },\n})\n\nconst ns = useNamespace('aside')\nconst style = computed(\n  () =>\n    (props.width ? ns.cssVarBlock({ width: props.width }) : {}) as CSSProperties\n)\n</script>\n"], "names": ["_openBlock", "_normalizeClass", "_unref", "_normalizeStyle"], "mappings": ";;;;mCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;AAWA,MAAM,OAAAA,+BAAyB,CAAA,OAAA,EAAA;AAC/B,QAAA,KAAc,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACZ,KACS,EAAAC,cAAW,CAAAD,KAAA,CAAA,KAAc,CAAA,CAAA;AAAyB,OAC7D,EAAA;;;;;;;;;;"}