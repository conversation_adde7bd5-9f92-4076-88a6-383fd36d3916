# 🚀 Phase 2: 智能体真实集成 - 详细开发计划

## 📋 **开发总览**

### **🎯 Phase 2 目标**
将现有的模拟智能体响应替换为真实的 `demo_interactive_agent.py` 功能，实现完整的 Human-in-the-Loop 交互式智能体系统。

### **⏱️ 预计开发时间**
- **总时间**: 5-8小时
- **技术难度**: 中等 (主要是异步适配和状态管理)
- **用户体验提升**: 从模拟响应到真实智能体能力

## 🔧 **Phase 2.1: 后端集成 (预计2-3小时)**

### **Step 1: 创建异步适配器**
```python
# 文件: web_turbine_interface/turbine_backend/app/services/langgraph_adapter.py
class LangGraphWebSocketAdapter:
    """LangGraph到WebSocket的异步适配器"""
    
    def __init__(self):
        self.agent = InteractiveLangGraphAgent()
        self.session_states = {}  # 会话状态管理
    
    async def process_query_stream(self, session_id: str, query: str):
        """流式处理查询，生成WebSocket消息"""
        # 1. 初始化会话状态
        # 2. 执行LangGraph工作流
        # 3. 转换为WebSocket消息格式
        # 4. 处理Human-in-the-Loop交互
```

### **Step 2: 集成InteractiveLangGraphAgent**
```python
# 修改: web_turbine_interface/turbine_backend/app/services/agent_service.py
class AgentService:
    def __init__(self):
        self.langgraph_adapter = LangGraphWebSocketAdapter()
        self.mode = "real"  # 切换到真实模式
    
    async def process_query(self, query: str, session_id: str):
        """使用真实智能体处理查询"""
        if self.mode == "real":
            async for message in self.langgraph_adapter.process_query_stream(session_id, query):
                yield message
        else:
            # 保留模拟模式作为备选
            async for message in self._simulate_response(query):
                yield message
```

### **Step 3: WebSocket消息协议扩展**
```python
# 新增消息类型
MESSAGE_TYPES = {
    # 现有类型
    "processing_start": "开始处理",
    "routing_analysis": "路由分析", 
    "tool_recommendation": "工具推荐",
    "data_retrieval": "数据检索",
    "ai_analysis": "AI分析",
    "final_answer": "最终回答",
    
    # 新增Human-in-the-Loop类型
    "tool_selection_request": "工具选择请求",
    "tool_selection_confirmed": "工具选择确认",
    "tool_execution_start": "工具执行开始",
    "tool_execution_result": "工具执行结果"
}
```

## 🔄 **Phase 2.2: 交互协议设计 (预计1-2小时)**

### **Step 1: Human-in-the-Loop消息流程**
```
用户查询 → 智能体分析 → 工具推荐 → 用户选择确认 → 工具执行 → AI分析 → 最终回答

WebSocket消息流:
1. user_query: {"type": "user_message", "content": "查询内容"}
2. tool_recommendation: {"type": "tool_selection_request", "tools": [...], "session_id": "xxx"}
3. user_selection: {"type": "tool_selection", "selected_tools": [...], "session_id": "xxx"}
4. tool_execution: {"type": "tool_execution_start", "tools": [...]}
5. tool_results: {"type": "tool_execution_result", "results": {...}}
6. final_answer: {"type": "final_answer", "content": "最终回答"}
```

### **Step 2: 会话状态管理**
```python
class SessionManager:
    """WebSocket会话状态管理"""
    
    def __init__(self):
        self.sessions = {}
    
    def create_session(self, websocket) -> str:
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            "websocket": websocket,
            "langgraph_state": None,
            "waiting_for_selection": False,
            "available_tools": [],
            "created_at": datetime.now()
        }
        return session_id
    
    async def handle_tool_selection(self, session_id: str, selected_tools: List[str]):
        """处理用户工具选择"""
        session = self.sessions[session_id]
        session["waiting_for_selection"] = False
        # 继续LangGraph工作流
        await self._continue_langgraph_workflow(session_id, selected_tools)
```

## 🎨 **Phase 2.3: 前端交互优化 (预计1-2小时)**

### **Step 1: 工具选择确认界面**
```vue
<!-- 增强现有的工具推荐卡片 -->
<div v-if="message.type === 'tool_selection_request'" class="tool-selection-request">
  <div class="selection-header">
    <h4>🤖 智能体推荐以下工具，请选择：</h4>
    <p class="selection-hint">您可以选择一个或多个工具，或让AI自动选择</p>
  </div>
  
  <div class="tools-grid">
    <div v-for="tool in message.tools" :key="tool.name" 
         class="tool-card selectable"
         :class="{ 'selected': selectedTools.includes(tool.name) }"
         @click="toggleToolSelection(tool.name)">
      <!-- 工具卡片内容 -->
    </div>
  </div>
  
  <div class="selection-actions">
    <el-button @click="confirmToolSelection" type="primary" :disabled="selectedTools.length === 0">
      确认选择 ({{ selectedTools.length }}个工具)
    </el-button>
    <el-button @click="selectAllTools" type="info">使用所有推荐工具</el-button>
    <el-button @click="useAIOnly" type="warning">仅使用AI分析</el-button>
  </div>
</div>
```

### **Step 2: 工具执行状态显示**
```vue
<!-- 工具执行进度显示 -->
<div v-if="message.type === 'tool_execution_start'" class="tool-execution-status">
  <div class="execution-header">
    <span class="status-icon">⚡</span>
    <span class="status-text">正在执行选定的工具...</span>
  </div>
  
  <div class="executing-tools">
    <div v-for="tool in message.tools" :key="tool" class="executing-tool">
      <div class="tool-icon">{{ getToolIcon(tool) }}</div>
      <div class="tool-name">{{ tool }}</div>
      <div class="tool-progress">
        <div class="progress-spinner"></div>
      </div>
    </div>
  </div>
</div>
```

## 🛠️ **Phase 2.4: 真实工具调用 (预计1-2小时)**

### **Step 1: MCP工具客户端集成**
```python
# 集成现有的MCP工具
class MCPToolsIntegration:
    def __init__(self):
        self.mcp_client = MCPClient()
        self.db_client = WindTurbineDBClient(self.mcp_client)
        self.mcp_tools = MCPToolsClientFixed()
    
    async def execute_tool(self, tool_name: str, query: str) -> Dict[str, Any]:
        """执行指定工具"""
        if tool_name == "wind-turbine-db":
            return await self._query_database(query)
        elif tool_name == "local-knowledge-base":
            return self._search_local_knowledge(query)
        elif tool_name == "context7":
            return self._search_context7(query)
        elif tool_name == "fetch":
            return self._fetch_online_info(query)
        elif tool_name == "pdf-processor":
            return self._process_pdf(query)
        elif tool_name == "filesystem":
            return self._access_filesystem(query)
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
```

### **Step 2: 错误处理和降级**
```python
class ToolExecutionManager:
    async def execute_tools_with_fallback(self, tools: List[str], query: str):
        """带降级的工具执行"""
        results = {}
        failed_tools = []
        
        for tool in tools:
            try:
                result = await self.execute_tool(tool, query)
                results[tool] = result
            except Exception as e:
                failed_tools.append({"tool": tool, "error": str(e)})
                # 记录错误但继续执行其他工具
        
        # 如果所有工具都失败，使用纯AI分析
        if not results and failed_tools:
            results["ai_only"] = await self._ai_only_analysis(query)
        
        return results, failed_tools
```

## 🧪 **Phase 2.5: 集成测试验证 (预计1小时)**

### **Playwright MCP测试场景**
```python
# 测试文件: web_turbine_interface/test_phase2_integration.py
async def test_real_agent_integration():
    """测试真实智能体集成"""
    
    # 1. 测试工具推荐
    await page.type("input", "风机齿轮箱故障诊断方法")
    await page.click("button[type='submit']")
    
    # 2. 等待工具推荐出现
    await page.wait_for_selector(".tool-selection-request")
    
    # 3. 选择工具
    await page.click(".tool-card[data-tool='wind-turbine-db']")
    await page.click("button:has-text('确认选择')")
    
    # 4. 验证工具执行
    await page.wait_for_selector(".tool-execution-status")
    
    # 5. 验证最终回答
    await page.wait_for_selector(".message[data-type='final_answer']")
    
    # 6. 验证回答质量
    final_answer = await page.text_content(".final-answer-content")
    assert "齿轮箱" in final_answer
    assert len(final_answer) > 100  # 确保回答有足够内容
```

## 📊 **成功标准**

### **功能完整性**
- ✅ 真实的6种MCP工具调用能力
- ✅ Human-in-the-Loop交互式工具选择
- ✅ 完整的LangGraph工作流集成
- ✅ 流式响应用户体验保持

### **用户体验**
- ✅ 保持Phase 1的商业级界面体验
- ✅ 工具选择过程直观易用
- ✅ 错误处理优雅友好
- ✅ 响应时间在可接受范围内

### **技术质量**
- ✅ 代码结构清晰，易于维护
- ✅ 异步处理性能良好
- ✅ 错误处理完善
- ✅ Playwright MCP测试覆盖

## 🎯 **Phase 2 完成后的系统能力**

1. **真实智能体能力** - 不再是模拟响应，而是真正的AI智能体
2. **6种工具真实调用** - 风机数据库、技术文档、在线搜索等真实工具
3. **Human-in-the-Loop交互** - 用户可以真正选择和确认工具使用
4. **专业技术问答** - 基于真实数据源的专业风机技术回答
5. **商业级用户体验** - 保持Phase 1的高质量界面体验

**🎊 Phase 2完成后，风机智能体Web版本将成为一个真正的专业级智能助手！**
