{"version": 3, "file": "util.mjs", "sources": ["../../../../../../packages/components/table/src/util.ts"], "sourcesContent": ["import { createVNode, isVNode, render } from 'vue'\nimport { flatMap, get, isNull, merge } from 'lodash-unified'\nimport {\n  ensureArray,\n  getProp,\n  hasOwn,\n  isArray,\n  isBoolean,\n  isFunction,\n  isNumber,\n  isObject,\n  isString,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport ElTooltip, {\n  type ElTooltipProps,\n} from '@element-plus/components/tooltip'\n\nimport type { DefaultRow, Table, TreeProps } from './table/defaults'\nimport type { TableColumnCtx } from './table-column/defaults'\nimport type { CSSProperties, VNode } from 'vue'\n\nexport type TableOverflowTooltipOptions = Partial<\n  Pick<\n    ElTooltipProps,\n    | 'appendTo'\n    | 'effect'\n    | 'enterable'\n    | 'hideAfter'\n    | 'offset'\n    | 'placement'\n    | 'popperClass'\n    | 'popperOptions'\n    | 'showAfter'\n    | 'showArrow'\n    | 'transition'\n  >\n>\n\nexport type TableOverflowTooltipFormatter<T extends DefaultRow> = (data: {\n  row: T\n  column: TableColumnCtx<T>\n  cellValue: any\n}) => VNode | string\n\ntype RemovePopperFn = (() => void) & {\n  trigger?: HTMLElement\n  vm?: VNode\n}\n\ntype CompareValue<T> = {\n  value: T\n  index: number\n  key: any[] | null\n}\n\nexport const getCell = function (event: Event) {\n  return (event.target as HTMLElement)?.closest('td')\n}\n\nexport const orderBy = function <T extends DefaultRow>(\n  array: T[],\n  sortKey: string | null,\n  reverse: string | number | null,\n  sortMethod: TableColumnCtx<T>['sortMethod'] | null,\n  sortBy: string | string[] | ((a: T, index: number, array?: T[]) => string)\n) {\n  if (\n    !sortKey &&\n    !sortMethod &&\n    (!sortBy || (isArray(sortBy) && !sortBy.length))\n  ) {\n    return array\n  }\n  if (isString(reverse)) {\n    reverse = reverse === 'descending' ? -1 : 1\n  } else {\n    reverse = reverse && reverse < 0 ? -1 : 1\n  }\n  const getKey = sortMethod\n    ? null\n    : function (value: T, index: number) {\n        if (sortBy) {\n          return flatMap(ensureArray(sortBy), (by) => {\n            if (isString(by)) {\n              return get(value, by)\n            } else {\n              return by(value, index, array)\n            }\n          })\n        }\n        if (sortKey !== '$key') {\n          if (isObject(value) && '$value' in value) value = value.$value\n        }\n        return [\n          isObject(value) ? (sortKey ? get(value, sortKey) : null) : value,\n        ]\n      }\n  const compare = function (a: CompareValue<T>, b: CompareValue<T>) {\n    if (sortMethod) {\n      return sortMethod(a.value, b.value)\n    }\n    for (let i = 0, len = a.key?.length ?? 0; i < len; i++) {\n      if (a.key?.[i] < b.key?.[i]) {\n        return -1\n      }\n      if (a.key?.[i] > b.key?.[i]) {\n        return 1\n      }\n    }\n    return 0\n  }\n  return array\n    .map<CompareValue<T>>((value: T, index) => {\n      return {\n        value,\n        index,\n        key: getKey ? getKey(value, index) : null,\n      }\n    })\n    .sort((a, b) => {\n      let order = compare(a, b)\n      if (!order) {\n        // make stable https://en.wikipedia.org/wiki/Sorting_algorithm#Stability\n        order = a.index - b.index\n      }\n      return order * +reverse\n    })\n    .map((item) => item.value)\n}\n\nexport const getColumnById = function <T extends DefaultRow>(\n  table: {\n    columns: TableColumnCtx<T>[]\n  },\n  columnId: string\n): null | TableColumnCtx<T> {\n  let column = null\n  table.columns.forEach((item) => {\n    if (item.id === columnId) {\n      column = item\n    }\n  })\n  return column\n}\n\nexport const getColumnByKey = function <T extends DefaultRow>(\n  table: {\n    columns: TableColumnCtx<T>[]\n  },\n  columnKey: string\n): TableColumnCtx<T> {\n  let column = null\n  for (let i = 0; i < table.columns.length; i++) {\n    const item = table.columns[i]\n    if (item.columnKey === columnKey) {\n      column = item\n      break\n    }\n  }\n  if (!column)\n    throwError('ElTable', `No column matching with column-key: ${columnKey}`)\n  return column\n}\n\nexport const getColumnByCell = function <T extends DefaultRow>(\n  table: {\n    columns: TableColumnCtx<T>[]\n  },\n  cell: HTMLElement,\n  namespace: string\n): null | TableColumnCtx<T> {\n  const matches = (cell.className || '').match(\n    new RegExp(`${namespace}-table_[^\\\\s]+`, 'gm')\n  )\n  if (matches) {\n    return getColumnById(table, matches[0])\n  }\n  return null\n}\n\nexport const getRowIdentity = <T extends DefaultRow>(\n  row: T,\n  rowKey: string | ((row: T) => string) | null,\n  isReturnRawValue: boolean = false\n): string => {\n  if (!row) throw new Error('Row is required when get row identity')\n  if (isString(rowKey)) {\n    if (!rowKey.includes('.')) {\n      return isReturnRawValue ? row[rowKey] : `${row[rowKey]}`\n    }\n    const key = rowKey.split('.')\n    let current: any = row\n    for (const element of key) {\n      current = current[element]\n    }\n    //TODO: \"current\" is now any, we just satisfies typecheck here\n    // but this function can actually return a number\n    return isReturnRawValue ? (current as string) : `${current}`\n  } else if (isFunction(rowKey)) {\n    return rowKey.call(null, row)\n  }\n  return ''\n}\n\nexport const getKeysMap = function <T extends DefaultRow>(\n  array: T[],\n  rowKey: string | null,\n  flatten = false,\n  childrenKey = 'children'\n): Record<PropertyKey, { row: T; index: number }> {\n  const data = array || []\n  const arrayMap: Record<string, { row: T; index: number }> = {}\n\n  data.forEach((row, index) => {\n    arrayMap[getRowIdentity(row, rowKey)] = { row, index }\n\n    if (flatten) {\n      const children = row[childrenKey]\n      if (isArray(children)) {\n        Object.assign(arrayMap, getKeysMap(children, rowKey, true, childrenKey))\n      }\n    }\n  })\n\n  return arrayMap\n}\n\nexport function mergeOptions<T extends DefaultRow, K extends DefaultRow>(\n  defaults: T,\n  config: K\n): T & K {\n  const options = {} as T & K\n  let key: keyof T & keyof K\n  for (key in defaults) {\n    options[key] = defaults[key]\n  }\n  for (key in config) {\n    if (hasOwn(config, key)) {\n      const value = config[key]\n      if (!isUndefined(value)) {\n        options[key as keyof K] = value\n      }\n    }\n  }\n  return options\n}\n\nexport function parseWidth(width?: number | string): number | string {\n  if (width === '') return width\n  if (!isUndefined(width)) {\n    width = Number.parseInt(width as string, 10)\n    if (Number.isNaN(width)) {\n      width = ''\n    }\n  }\n  return width!\n}\n\nexport function parseMinWidth(minWidth: number | string): number | string {\n  if (minWidth === '') return minWidth\n  if (!isUndefined(minWidth)) {\n    minWidth = parseWidth(minWidth)\n    if (Number.isNaN(minWidth)) {\n      minWidth = 80\n    }\n  }\n  return minWidth\n}\n\nexport function parseHeight(height: number | string | null) {\n  if (isNumber(height)) {\n    return height\n  }\n  if (isString(height)) {\n    if (/^\\d+(?:px)?$/.test(height)) {\n      return Number.parseInt(height, 10)\n    } else {\n      return height\n    }\n  }\n  return null\n}\n\n// https://github.com/reduxjs/redux/blob/master/src/compose.ts\nexport function compose(...funcs: ((...args: any[]) => void)[]) {\n  if (funcs.length === 0) {\n    return <T>(arg: T) => arg\n  }\n  if (funcs.length === 1) {\n    return funcs[0]\n  }\n  return funcs.reduce(\n    (a, b) =>\n      (...args: any[]) =>\n        a(b(...args))\n  )\n}\n\nexport function toggleRowStatus<T extends DefaultRow>(\n  statusArr: T[],\n  row: T,\n  newVal?: boolean,\n  tableTreeProps?: TreeProps,\n  selectable?: ((row: T, index: number) => boolean) | null,\n  rowIndex?: number,\n  rowKey?: string | null\n): boolean {\n  let _rowIndex = rowIndex ?? 0\n  let changed = false\n\n  const getIndex = () => {\n    if (!rowKey) {\n      return statusArr.indexOf(row)\n    }\n\n    const id = getRowIdentity(row, rowKey)\n\n    return statusArr.findIndex((item) => getRowIdentity(item, rowKey) === id)\n  }\n\n  const index = getIndex()\n\n  const included = index !== -1\n  const isRowSelectable = selectable?.call(null, row, _rowIndex)\n\n  const toggleStatus = (type: 'add' | 'remove') => {\n    if (type === 'add') {\n      statusArr.push(row)\n    } else {\n      statusArr.splice(index, 1)\n    }\n    changed = true\n  }\n  const getChildrenCount = <T extends DefaultRow>(row: T) => {\n    let count = 0\n    const children = tableTreeProps?.children && row[tableTreeProps.children]\n    if (children && isArray(children)) {\n      count += children.length\n      children.forEach((item) => {\n        count += getChildrenCount(item)\n      })\n    }\n    return count\n  }\n\n  if (!selectable || isRowSelectable) {\n    if (isBoolean(newVal)) {\n      if (newVal && !included) {\n        toggleStatus('add')\n      } else if (!newVal && included) {\n        toggleStatus('remove')\n      }\n    } else {\n      included ? toggleStatus('remove') : toggleStatus('add')\n    }\n  }\n\n  if (\n    !tableTreeProps?.checkStrictly &&\n    tableTreeProps?.children &&\n    isArray(row[tableTreeProps.children])\n  ) {\n    row[tableTreeProps.children].forEach((item: T) => {\n      const childChanged = toggleRowStatus(\n        statusArr,\n        item,\n        newVal ?? !included,\n        tableTreeProps,\n        selectable,\n        _rowIndex + 1,\n        rowKey\n      )\n      _rowIndex += getChildrenCount(item) + 1\n      if (childChanged) {\n        changed = childChanged\n      }\n    })\n  }\n  return changed\n}\n\nexport function walkTreeNode<T extends DefaultRow>(\n  root: T[],\n  cb: (parent: any, children: T | T[] | null, level: number) => void,\n  childrenKey = 'children',\n  lazyKey = 'hasChildren',\n  lazy = false\n) {\n  const isNil = (array: any): array is null => !(isArray(array) && array.length)\n\n  function _walker(parent: any, children: T | T[], level: number) {\n    cb(parent, children, level)\n    children.forEach((item: any) => {\n      if (item[lazyKey] && lazy) {\n        cb(item, null, level + 1)\n        return\n      }\n      const children = item[childrenKey]\n      if (!isNil(children)) {\n        _walker(item, children, level + 1)\n      }\n    })\n  }\n\n  root.forEach((item: any) => {\n    if (item[lazyKey] && lazy) {\n      cb(item, null, 0)\n      return\n    }\n    const children = item[childrenKey]\n    if (!isNil(children)) {\n      _walker(item, children, 0)\n    }\n  })\n}\n\nconst getTableOverflowTooltipProps = <T extends DefaultRow>(\n  props: TableOverflowTooltipOptions,\n  innerText: string,\n  row: T,\n  column: TableColumnCtx<T> | null\n) => {\n  // merge popperOptions\n  const popperOptions = {\n    strategy: 'fixed',\n    ...props.popperOptions,\n  }\n\n  const tooltipFormatterContent = isFunction(column?.tooltipFormatter)\n    ? column.tooltipFormatter({\n        row,\n        column,\n        cellValue: getProp(row, column.property).value,\n      })\n    : undefined\n\n  if (isVNode(tooltipFormatterContent)) {\n    return {\n      slotContent: tooltipFormatterContent,\n      content: null,\n      ...props,\n      popperOptions,\n    }\n  }\n\n  return {\n    slotContent: null,\n    content: tooltipFormatterContent ?? innerText,\n    ...props,\n    popperOptions,\n  }\n}\n\nexport let removePopper: RemovePopperFn | null = null\n\nexport function createTablePopper<T extends DefaultRow>(\n  props: TableOverflowTooltipOptions,\n  popperContent: string,\n  row: T,\n  column: TableColumnCtx<T> | null,\n  trigger: HTMLElement | null,\n  table: Table<DefaultRow>\n) {\n  const tableOverflowTooltipProps = getTableOverflowTooltipProps(\n    props,\n    popperContent,\n    row,\n    column\n  )\n  const mergedProps = {\n    ...tableOverflowTooltipProps,\n    slotContent: undefined,\n  }\n  if (removePopper?.trigger === trigger) {\n    const comp = removePopper.vm?.component\n    merge(comp?.props, mergedProps)\n    if (comp && tableOverflowTooltipProps.slotContent) {\n      comp.slots.content = () => [tableOverflowTooltipProps.slotContent]\n    }\n    return\n  }\n  removePopper?.()\n  const parentNode = table?.refs.tableWrapper\n  const ns = parentNode?.dataset.prefix\n  const vm = createVNode(\n    ElTooltip,\n    {\n      virtualTriggering: true,\n      virtualRef: trigger,\n      appendTo: parentNode,\n      placement: 'top',\n      transition: 'none', // Default does not require transition\n      offset: 0,\n      hideAfter: 0,\n      ...mergedProps,\n    },\n    tableOverflowTooltipProps.slotContent\n      ? {\n          content: () => tableOverflowTooltipProps.slotContent,\n        }\n      : undefined\n  )\n  vm.appContext = { ...table.appContext, ...table }\n  const container = document.createElement('div')\n  render(vm, container)\n  vm.component!.exposed!.onOpen()\n  const scrollContainer = parentNode?.querySelector(`.${ns}-scrollbar__wrap`)\n  removePopper = () => {\n    render(null, container)\n    scrollContainer?.removeEventListener('scroll', removePopper!)\n    removePopper = null\n  }\n  removePopper.trigger = trigger ?? undefined\n  removePopper.vm = vm\n  scrollContainer?.addEventListener('scroll', removePopper)\n}\n\nfunction getCurrentColumns<T extends DefaultRow>(\n  column: TableColumnCtx<T>\n): TableColumnCtx<T>[] {\n  if (column.children) {\n    return flatMap(column.children, getCurrentColumns)\n  } else {\n    return [column]\n  }\n}\n\nfunction getColSpan<T extends DefaultRow>(\n  colSpan: number,\n  column: TableColumnCtx<T>\n) {\n  return colSpan + column.colSpan\n}\n\nexport const isFixedColumn = <T extends DefaultRow>(\n  index: number,\n  fixed: string | boolean | undefined,\n  store: any,\n  realColumns?: TableColumnCtx<T>[]\n) => {\n  let start = 0\n  let after = index\n  const columns = store.states.columns.value\n  if (realColumns) {\n    // fixed column supported in grouped header\n    const curColumns = getCurrentColumns(realColumns[index])\n    const preColumns = columns.slice(0, columns.indexOf(curColumns[0]))\n\n    start = preColumns.reduce(getColSpan, 0)\n    after = start + curColumns.reduce(getColSpan, 0) - 1\n  } else {\n    start = index\n  }\n  let fixedLayout\n  switch (fixed) {\n    case 'left':\n      if (after < store.states.fixedLeafColumnsLength.value) {\n        fixedLayout = 'left'\n      }\n      break\n    case 'right':\n      if (\n        start >=\n        columns.length - store.states.rightFixedLeafColumnsLength.value\n      ) {\n        fixedLayout = 'right'\n      }\n      break\n    default:\n      if (after < store.states.fixedLeafColumnsLength.value) {\n        fixedLayout = 'left'\n      } else if (\n        start >=\n        columns.length - store.states.rightFixedLeafColumnsLength.value\n      ) {\n        fixedLayout = 'right'\n      }\n  }\n  return fixedLayout\n    ? {\n        direction: fixedLayout,\n        start,\n        after,\n      }\n    : {}\n}\n\nexport const getFixedColumnsClass = <T extends DefaultRow>(\n  namespace: string,\n  index: number,\n  fixed: string | boolean | undefined,\n  store: any,\n  realColumns?: TableColumnCtx<T>[],\n  offset = 0\n) => {\n  const classes: string[] = []\n  const { direction, start, after } = isFixedColumn(\n    index,\n    fixed,\n    store,\n    realColumns\n  )\n  if (direction) {\n    const isLeft = direction === 'left'\n    classes.push(`${namespace}-fixed-column--${direction}`)\n    if (\n      isLeft &&\n      after + offset === store.states.fixedLeafColumnsLength.value - 1\n    ) {\n      classes.push('is-last-column')\n    } else if (\n      !isLeft &&\n      start - offset ===\n        store.states.columns.value.length -\n          store.states.rightFixedLeafColumnsLength.value\n    ) {\n      classes.push('is-first-column')\n    }\n  }\n  return classes\n}\n\nfunction getOffset<T extends DefaultRow>(\n  offset: number,\n  column: TableColumnCtx<T>\n) {\n  return (\n    offset +\n    (isNull(column.realWidth) || Number.isNaN(column.realWidth)\n      ? Number(column.width)\n      : column.realWidth)\n  )\n}\n\nexport const getFixedColumnOffset = <T extends DefaultRow>(\n  index: number,\n  fixed: string | boolean | undefined,\n  store: any,\n  realColumns?: TableColumnCtx<T>[]\n) => {\n  const {\n    direction,\n    start = 0,\n    after = 0,\n  } = isFixedColumn(index, fixed, store, realColumns)\n  if (!direction) {\n    return\n  }\n  const styles: CSSProperties = {}\n  const isLeft = direction === 'left'\n  const columns = store.states.columns.value\n  if (isLeft) {\n    styles.left = columns.slice(0, start).reduce(getOffset, 0)\n  } else {\n    styles.right = columns\n      .slice(after + 1)\n      .reverse()\n      .reduce(getOffset, 0)\n  }\n  return styles\n}\n\nexport const ensurePosition = (\n  style: CSSProperties | undefined,\n  key: keyof CSSProperties\n) => {\n  if (!style) return\n  if (!Number.isNaN(style[key])) {\n    style[key] = `${style[key]}px` as any\n  }\n}\n"], "names": ["ensureArray"], "mappings": ";;;;;;;;AAgBY,MAAC,OAAO,GAAG,SAAS,KAAK,EAAE;AACvC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjE,EAAE;AACU,MAAC,OAAO,GAAG,SAAS,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;AAC7E,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;AACjF,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;AACzB,IAAI,OAAO,GAAG,OAAO,KAAK,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,GAAG,MAAM;AACT,IAAI,OAAO,GAAG,OAAO,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,KAAK,EAAE,KAAK,EAAE;AAC5D,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,OAAO,CAACA,SAAW,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK;AAClD,QAAQ,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC1B,UAAU,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChC,SAAS,MAAM;AACf,UAAU,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,IAAI,KAAK;AAC9C,QAAQ,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO;AACX,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,IAAI,GAAG,KAAK;AACpE,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;AACjC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC3G,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7F,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,OAAO;AACP,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7F,QAAQ,OAAO,CAAC,CAAC;AACjB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,CAAC;AACJ,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;AACrC,IAAI,OAAO;AACX,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,IAAI;AAC/C,KAAK,CAAC;AACN,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACpB,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,KAAK,GAAG,CAAC,OAAO,CAAC;AAC5B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE;AACU,MAAC,aAAa,GAAG,SAAS,KAAK,EAAE,QAAQ,EAAE;AACvD,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAClC,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,QAAQ,EAAE;AAC9B,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,cAAc,GAAG,SAAS,KAAK,EAAE,SAAS,EAAE;AACzD,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;AACtC,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,MAAM;AACb,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9E,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,eAAe,GAAG,SAAS,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE;AAChE,EAAE,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/F,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,OAAO,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACU,MAAC,cAAc,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,gBAAgB,GAAG,KAAK,KAAK;AACzE,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AAC7D,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC/B,MAAM,OAAO,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,OAAO,GAAG,GAAG,CAAC;AACtB,IAAI,KAAK,MAAM,OAAO,IAAI,GAAG,EAAE;AAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,gBAAgB,GAAG,OAAO,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACrD,GAAG,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;AACjC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE;AACU,MAAC,UAAU,GAAG,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO,GAAG,KAAK,EAAE,WAAW,GAAG,UAAU,EAAE;AAC7F,EAAE,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;AAC3B,EAAE,MAAM,QAAQ,GAAG,EAAE,CAAC;AACtB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AAC/B,IAAI,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AAC3D,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;AACxC,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC7B,QAAQ,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;AACjF,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,QAAQ,CAAC;AAClB,EAAE;AACK,SAAS,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE;AAC/C,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,KAAK,GAAG,IAAI,QAAQ,EAAE;AACxB,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,KAAK,GAAG,IAAI,MAAM,EAAE;AACtB,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AAC7B,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC/B,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC7B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACM,SAAS,UAAU,CAAC,KAAK,EAAE;AAClC,EAAE,IAAI,KAAK,KAAK,EAAE;AAClB,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC3B,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACvC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC7B,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACM,SAAS,aAAa,CAAC,QAAQ,EAAE;AACxC,EAAE,IAAI,QAAQ,KAAK,EAAE;AACrB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;AAC9B,IAAI,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAChC,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACM,SAAS,WAAW,CAAC,MAAM,EAAE;AACpC,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxB,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxB,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACrC,MAAM,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACzC,KAAK,MAAM;AACX,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACM,SAAS,OAAO,CAAC,GAAG,KAAK,EAAE;AAClC,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC;AACM,SAAS,eAAe,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE;AACtG,EAAE,IAAI,SAAS,GAAG,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC;AACtB,EAAE,MAAM,QAAQ,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,MAAM,EAAE,GAAG,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAC9E,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;AAChC,EAAE,MAAM,eAAe,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAC9F,EAAE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK;AACjC,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB,MAAM,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,GAAG,IAAI,CAAC;AACnB,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,IAAI,KAAK;AACrC,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,MAAM,QAAQ,GAAG,CAAC,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,QAAQ,KAAK,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAClH,IAAI,IAAI,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;AACvC,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;AAC/B,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACjC,QAAQ,KAAK,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACxC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,UAAU,IAAI,eAAe,EAAE;AACtC,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AAC3B,MAAM,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;AAC/B,QAAQ,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5B,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE;AACtC,QAAQ,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC/B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AAC9D,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,aAAa,CAAC,KAAK,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE;AACjL,IAAI,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACnD,MAAM,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,IAAI,IAAI,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;AACpJ,MAAM,SAAS,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9C,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,OAAO,GAAG,YAAY,CAAC;AAC/B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACM,SAAS,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,WAAW,GAAG,UAAU,EAAE,OAAO,GAAG,aAAa,EAAE,IAAI,GAAG,KAAK,EAAE;AACxG,EAAE,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;AAC7D,EAAE,SAAS,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC5C,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC/B,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;AACjC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAClC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AAC7B,QAAQ,OAAO,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAC5C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACzB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;AAC/B,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACxB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAC1B,MAAM,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AACjC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,4BAA4B,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,KAAK;AACxE,EAAE,MAAM,aAAa,GAAG;AACxB,IAAI,QAAQ,EAAE,OAAO;AACrB,IAAI,GAAG,KAAK,CAAC,aAAa;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,UAAU,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAC1H,IAAI,GAAG;AACP,IAAI,MAAM;AACV,IAAI,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK;AAClD,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AACd,EAAE,IAAI,OAAO,CAAC,uBAAuB,CAAC,EAAE;AACxC,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,GAAG,KAAK;AACd,MAAM,aAAa;AACnB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO;AACT,IAAI,WAAW,EAAE,IAAI;AACrB,IAAI,OAAO,EAAE,uBAAuB,IAAI,IAAI,GAAG,uBAAuB,GAAG,SAAS;AAClF,IAAI,GAAG,KAAK;AACZ,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ,CAAC,CAAC;AACQ,IAAC,YAAY,GAAG,KAAK;AACxB,SAAS,iBAAiB,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;AACrF,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,yBAAyB,GAAG,4BAA4B,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AACpG,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,GAAG,yBAAyB;AAChC,IAAI,WAAW,EAAE,KAAK,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,OAAO,MAAM,OAAO,EAAE;AAC1E,IAAI,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC;AACxE,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC3D,IAAI,IAAI,IAAI,IAAI,yBAAyB,CAAC,WAAW,EAAE;AACvD,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;AACzE,KAAK;AACL,IAAI,OAAO;AACX,GAAG;AACH,EAAE,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,EAAE,CAAC;AACjD,EAAE,MAAM,UAAU,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;AACtE,EAAE,MAAM,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACrE,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC,SAAS,EAAE;AACpC,IAAI,iBAAiB,EAAE,IAAI;AAC3B,IAAI,UAAU,EAAE,OAAO;AACvB,IAAI,QAAQ,EAAE,UAAU;AACxB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,SAAS,EAAE,CAAC;AAChB,IAAI,GAAG,WAAW;AAClB,GAAG,EAAE,yBAAyB,CAAC,WAAW,GAAG;AAC7C,IAAI,OAAO,EAAE,MAAM,yBAAyB,CAAC,WAAW;AACxD,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;AACd,EAAE,EAAE,CAAC,UAAU,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,GAAG,KAAK,EAAE,CAAC;AACpD,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACxB,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;AAChC,EAAE,MAAM,eAAe,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3G,EAAE,YAAY,GAAG,MAAM;AACvB,IAAI,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC5B,IAAI,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACnG,IAAI,YAAY,GAAG,IAAI,CAAC;AACxB,GAAG,CAAC;AACJ,EAAE,YAAY,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AAC5D,EAAE,YAAY,CAAC,EAAE,GAAG,EAAE,CAAC;AACvB,EAAE,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAC9F,CAAC;AACD,SAAS,iBAAiB,CAAC,MAAM,EAAE;AACnC,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;AACvD,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;AACpB,GAAG;AACH,CAAC;AACD,SAAS,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;AACrC,EAAE,OAAO,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAClC,CAAC;AACW,MAAC,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,KAAK;AACnE,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC;AACpB,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7C,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7D,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAC7C,IAAI,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACzD,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,KAAK,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,QAAQ,KAAK;AACf,IAAI,KAAK,MAAM;AACf,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE;AAC7D,QAAQ,WAAW,GAAG,MAAM,CAAC;AAC7B,OAAO;AACP,MAAM,MAAM;AACZ,IAAI,KAAK,OAAO;AAChB,MAAM,IAAI,KAAK,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE;AACpF,QAAQ,WAAW,GAAG,OAAO,CAAC;AAC9B,OAAO;AACP,MAAM,MAAM;AACZ,IAAI;AACJ,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE;AAC7D,QAAQ,WAAW,GAAG,MAAM,CAAC;AAC7B,OAAO,MAAM,IAAI,KAAK,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE;AAC3F,QAAQ,WAAW,GAAG,OAAO,CAAC;AAC9B,OAAO;AACP,GAAG;AACH,EAAE,OAAO,WAAW,GAAG;AACvB,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,KAAK;AACT,IAAI,KAAK;AACT,GAAG,GAAG,EAAE,CAAC;AACT,EAAE;AACU,MAAC,oBAAoB,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,GAAG,CAAC,KAAK;AACjG,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACtF,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC;AACxC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC5D,IAAI,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,GAAG,CAAC,EAAE;AACpF,MAAM,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACrC,KAAK,MAAM,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE;AACjI,MAAM,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;AACnC,EAAE,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACzH,CAAC;AACW,MAAC,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,KAAK;AAC1E,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,KAAK,GAAG,CAAC;AACb,GAAG,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACtD,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO;AACX,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,MAAM,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC;AACtC,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7C,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC/D,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AAC3E,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,EAAE;AACU,MAAC,cAAc,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC9C,EAAE,IAAI,CAAC,KAAK;AACZ,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;AACjC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACnC,GAAG;AACH;;;;"}