{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/page-header/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport PageHeader from './src/page-header.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElPageHeader: SFCWithInstall<typeof PageHeader> =\n  withInstall(PageHeader)\nexport default ElPageHeader\n\nexport * from './src/page-header'\n"], "names": ["withInstall", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;AAEY,MAAC,YAAY,GAAGA,mBAAW,CAACC,uBAAU;;;;;;;"}