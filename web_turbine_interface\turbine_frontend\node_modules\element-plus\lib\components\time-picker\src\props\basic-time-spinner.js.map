{"version": 3, "file": "basic-time-spinner.js", "sources": ["../../../../../../../packages/components/time-picker/src/props/basic-time-spinner.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { disabledTimeListsProps } from '../props/shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const basicTimeSpinnerProps = buildProps({\n  role: {\n    type: String,\n    required: true,\n  },\n  spinnerDate: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  showSeconds: {\n    type: Boolean,\n    default: true,\n  },\n  arrowControl: Boolean,\n  amPmMode: {\n    // 'a': am/pm; 'A': AM/PM\n    type: definePropType<'a' | 'A' | ''>(String),\n    default: '',\n  },\n  ...disabledTimeListsProps,\n} as const)\n\nexport type BasicTimeSpinnerProps = ExtractPropTypes<\n  typeof basicTimeSpinnerProps\n>\nexport type BasicTimeSpinnerPropsPublic = __ExtractPublicPropTypes<\n  typeof basicTimeSpinnerProps\n>\n"], "names": ["buildProps", "definePropType", "disabledTimeListsProps"], "mappings": ";;;;;;;AAEY,MAAC,qBAAqB,GAAGA,kBAAU,CAAC;AAChD,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,GAAGC,6BAAsB;AAC3B,CAAC;;;;"}