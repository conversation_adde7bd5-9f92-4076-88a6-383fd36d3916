@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'mixins/utils' as *;
@use 'common/var' as *;
@use 'common/transition';

@mixin menu-item {
  display: flex;
  align-items: center;
  height: getCssVar('menu-item-height');
  line-height: getCssVar('menu-item-height');
  font-size: getCssVar('menu-item-font-size');
  color: getCssVar('menu-text-color');
  padding: 0 getCssVar('menu-base-level-padding');
  list-style: none;
  cursor: pointer;
  position: relative;
  transition: border-color getCssVar('transition-duration'),
    background-color getCssVar('transition-duration'),
    color getCssVar('transition-duration');
  box-sizing: border-box;
  white-space: nowrap;

  * {
    vertical-align: bottom;
  }

  i {
    color: inherit;
  }

  &:hover,
  &:focus {
    outline: none;
  }

  &:hover {
    background-color: getCssVar('menu-hover-bg-color');
  }

  @include when(disabled) {
    opacity: 0.25;
    cursor: not-allowed;
    background: none !important;
  }
}

:root {
  // They are defined on :root so they can be inherited by sub-menu instead of overwritten
  @include set-component-css-var('menu', $menu);
}

@include b(menu) {
  border-right: solid 1px getCssVar('menu-border-color');
  list-style: none;
  position: relative;
  margin: 0;
  padding-left: 0;
  background-color: getCssVar('menu-bg-color');
  box-sizing: border-box;

  @include m(vertical) {
    &:not(.#{$namespace}-menu--collapse):not(.#{$namespace}-menu--popup-container) {
      & .#{$namespace}-menu-item,
      & .#{$namespace}-sub-menu__title,
      & .#{$namespace}-menu-item-group__title {
        white-space: nowrap;
        padding-left: calc(
          #{getCssVar('menu-base-level-padding')} + #{getCssVar('menu-level')} *
            #{getCssVar('menu-level-padding')}
        );
      }
    }
  }

  &:not(.#{$namespace}-menu--collapse) .#{$namespace}-sub-menu__title {
    padding-right: calc(
      #{getCssVar('menu-base-level-padding')} + #{getCssVar('menu-icon-width')}
    );
  }

  @include m(horizontal) {
    display: flex;
    flex-wrap: nowrap;
    border-right: none;

    height: getCssVar('menu-horizontal-height');

    // reset menu-item popup height
    &.#{$namespace}-menu--popup-container {
      height: unset;
    }

    &.#{$namespace}-menu {
      border-bottom: solid 1px getCssVar('menu-border-color');
    }

    & > .#{$namespace}-menu-item {
      display: inline-flex;
      justify-content: center;
      align-items: center;

      height: 100%;
      margin: 0;
      border-bottom: 2px solid transparent;
      color: getCssVar('menu-text-color');

      a,
      a:hover {
        color: inherit;
      }
    }

    & > .#{$namespace}-sub-menu {
      &:focus,
      &:hover {
        outline: none;
      }

      &:hover {
        .#{$namespace}-sub-menu__title {
          color: getCssVar('menu-hover-text-color');
        }
      }

      &.is-active {
        .#{$namespace}-sub-menu__title {
          border-bottom: 2px solid getCssVar('menu-active-color');
          color: getCssVar('menu-active-color');
        }
      }

      & .#{$namespace}-sub-menu__title {
        height: 100%;
        border-bottom: 2px solid transparent;
        color: getCssVar('menu-text-color');

        &:hover {
          background-color: getCssVar('menu-bg-color');
        }
      }
    }

    & .#{$namespace}-menu {
      & .#{$namespace}-menu-item,
      & .#{$namespace}-sub-menu__title {
        background-color: getCssVar('menu-bg-color');
        display: flex;
        align-items: center;
        height: getCssVar('menu-horizontal-sub-item-height');
        line-height: getCssVar('menu-horizontal-sub-item-height');
        padding: 0 10px;
        color: getCssVar('menu-text-color');
      }

      & .#{$namespace}-sub-menu__title {
        padding-right: 40px;
      }

      & .#{$namespace}-menu-item.is-active,
      & .#{$namespace}-menu-item.is-active:hover,
      & .#{$namespace}-sub-menu.is-active > .#{$namespace}-sub-menu__title,
      &
        .#{$namespace}-sub-menu.is-active
        > .#{$namespace}-sub-menu__title:hover {
        color: getCssVar('menu-active-color');
      }
    }

    & .#{$namespace}-menu-item:not(.is-disabled):hover,
    & .#{$namespace}-menu-item:not(.is-disabled):focus {
      outline: none;
      color: getCssVarWithDefault(
        'menu-active-color',
        getCssVar('menu-hover-text-color')
      );
      background-color: getCssVar('menu-hover-bg-color');
    }

    & > .#{$namespace}-menu-item.is-active {
      border-bottom: 2px solid getCssVar('menu-active-color');
      color: getCssVar('menu-active-color') !important;
    }
  }

  @include m(collapse) {
    width: calc(
      #{getCssVar('menu-icon-width')} + #{getCssVar('menu-base-level-padding')} *
        2
    );

    > .#{$namespace}-menu-item,
    > .#{$namespace}-sub-menu > .#{$namespace}-sub-menu__title,
    > .#{$namespace}-menu-item-group
      > ul
      > .#{$namespace}-sub-menu
      > .#{$namespace}-sub-menu__title {
      [class^='#{$namespace}-icon'] {
        margin: 0;
        vertical-align: middle;
        width: getCssVar('menu-icon-width');
        text-align: center;
      }

      .#{$namespace}-sub-menu__icon-arrow {
        display: none;
      }

      > span {
        height: 0;
        width: 0;
        overflow: hidden;
        visibility: hidden;
        display: inline-block;
      }
    }

    > .#{$namespace}-menu-item.is-active i {
      color: inherit;
    }

    .#{$namespace}-menu .#{$namespace}-sub-menu {
      min-width: 200px;
    }

    .#{$namespace}-sub-menu {
      &.is-active .#{$namespace}-sub-menu__title {
        color: getCssVar('menu-active-color');
      }
    }
  }

  @include m(popup) {
    z-index: 100;
    min-width: 200px;
    border: none;
    padding: 5px 0;
    border-radius: getCssVar('border-radius-small');
    box-shadow: getCssVar('box-shadow-light');
  }

  .#{$namespace}-icon {
    flex-shrink: 0;
  }
}

@include b(menu-item) {
  @include menu-item;

  & [class^='#{$namespace}-icon'] {
    margin-right: 5px;
    width: getCssVar('menu-icon-width');
    text-align: center;
    font-size: 18px;
    vertical-align: middle;
  }

  @include when(active) {
    color: getCssVar('menu-active-color');

    i {
      color: inherit;
    }
  }

  .#{$namespace}-menu-tooltip__trigger {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 getCssVar('menu-base-level-padding');
  }
}

@include b(sub-menu) {
  list-style: none;
  margin: 0;
  padding-left: 0;

  @include e(title) {
    @include menu-item;

    &:hover {
      background-color: getCssVar('menu-hover-bg-color');
    }
  }

  & .#{$namespace}-menu {
    border: none;
  }

  & .#{$namespace}-menu-item {
    height: getCssVar('menu-sub-item-height');
    line-height: getCssVar('menu-sub-item-height');
  }

  @include e(hide-arrow) {
    .#{$namespace}-sub-menu__icon-arrow {
      display: none !important;
    }
  }

  @include when(active) {
    .#{$namespace}-sub-menu__title {
      border-bottom-color: getCssVar('menu-active-color');
    }
  }

  @include when(disabled) {
    .#{$namespace}-sub-menu__title,
    .#{$namespace}-menu-item {
      opacity: 0.25;
      cursor: not-allowed;
      background: none !important;
    }
  }

  .#{$namespace}-icon {
    vertical-align: middle;
    margin-right: 5px;
    width: getCssVar('menu-icon-width');
    text-align: center;
    font-size: 18px;

    &.#{$namespace}-sub-menu__icon-more {
      margin-right: 0 !important;
    }
  }

  .#{$namespace}-sub-menu__icon-arrow {
    position: absolute;
    top: 50%;
    right: getCssVar('menu-base-level-padding');
    margin-top: -6px;
    transition: transform getCssVar('transition-duration');
    font-size: 12px;
    margin-right: 0;
    width: inherit;
  }
}

@include b(menu-item-group) {
  > ul {
    padding: 0;
  }

  @include e(title) {
    padding: 7px 0 7px getCssVar('menu-base-level-padding');
    line-height: normal;
    font-size: 12px;
    color: getCssVar('text-color', 'secondary');
  }
}

.horizontal-collapse-transition
  .#{$namespace}-sub-menu__title
  .#{$namespace}-sub-menu__icon-arrow {
  transition: getCssVar('transition-duration-fast');
  opacity: 0;
}
