{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-throttle-render/index.ts"], "sourcesContent": ["import { onMounted, ref, watch } from 'vue'\nimport { isNumber, isObject, isUndefined } from '@element-plus/utils'\n\nimport type { Ref } from 'vue'\n\nexport type ThrottleType =\n  | { leading?: number; trailing?: number; initVal?: boolean }\n  | number\n\nexport const useThrottleRender = (\n  loading: Ref<boolean>,\n  throttle: ThrottleType = 0\n) => {\n  if (throttle === 0) return loading\n  const initVal = isObject(throttle) && Boolean(throttle.initVal)\n  const throttled = ref(initVal)\n  let timeoutHandle: ReturnType<typeof setTimeout> | null = null\n\n  const dispatchThrottling = (timer: number | undefined) => {\n    if (isUndefined(timer)) {\n      throttled.value = loading.value\n      return\n    }\n    if (timeoutHandle) {\n      clearTimeout(timeoutHandle)\n    }\n    timeoutHandle = setTimeout(() => {\n      throttled.value = loading.value\n    }, timer)\n  }\n\n  const dispatcher = (type: 'leading' | 'trailing') => {\n    if (type === 'leading') {\n      if (isNumber(throttle)) {\n        dispatchThrottling(throttle)\n      } else {\n        dispatchThrottling(throttle.leading)\n      }\n    } else {\n      if (isObject(throttle)) {\n        dispatchThrottling(throttle.trailing)\n      } else {\n        throttled.value = false\n      }\n    }\n  }\n\n  onMounted(() => dispatcher('leading'))\n\n  watch(\n    () => loading.value,\n    (val) => {\n      dispatcher(val ? 'leading' : 'trailing')\n    }\n  )\n\n  return throttled\n}\n"], "names": ["isObject", "ref", "isUndefined", "isNumber", "onMounted", "watch"], "mappings": ";;;;;;;;AAEY,MAAC,iBAAiB,GAAG,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,KAAK;AAC5D,EAAE,IAAI,QAAQ,KAAK,CAAC;AACpB,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,MAAM,OAAO,GAAGA,eAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAClE,EAAE,MAAM,SAAS,GAAGC,OAAG,CAAC,OAAO,CAAC,CAAC;AACjC,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B,EAAE,MAAM,kBAAkB,GAAG,CAAC,KAAK,KAAK;AACxC,IAAI,IAAIC,iBAAW,CAAC,KAAK,CAAC,EAAE;AAC5B,MAAM,SAAS,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AACtC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,YAAY,CAAC,aAAa,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,aAAa,GAAG,UAAU,CAAC,MAAM;AACrC,MAAM,SAAS,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AACtC,KAAK,EAAE,KAAK,CAAC,CAAC;AACd,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,IAAI,KAAK;AAC/B,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;AAC5B,MAAM,IAAIC,cAAQ,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAQ,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACrC,OAAO,MAAM;AACb,QAAQ,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC7C,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAIH,eAAQ,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAQ,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9C,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAEI,aAAS,CAAC,MAAM,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;AACzC,EAAEC,SAAK,CAAC,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK;AACtC,IAAI,UAAU,CAAC,GAAG,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,SAAS,CAAC;AACnB;;;;"}