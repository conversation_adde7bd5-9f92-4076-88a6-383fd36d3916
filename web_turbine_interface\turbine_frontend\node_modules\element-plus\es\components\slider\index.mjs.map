{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/slider/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Slider from './src/slider.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSlider: SFCWithInstall<typeof Slider> = withInstall(Slider)\nexport default ElSlider\n\nexport * from './src/slider'\nexport * from './src/constants'\n"], "names": [], "mappings": ";;;;;AAEY,MAAC,QAAQ,GAAG,WAAW,CAAC,MAAM;;;;"}