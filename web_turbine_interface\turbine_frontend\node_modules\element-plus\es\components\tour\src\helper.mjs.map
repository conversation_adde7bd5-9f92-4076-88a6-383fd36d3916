{"version": 3, "file": "helper.mjs", "sources": ["../../../../../../packages/components/tour/src/helper.ts"], "sourcesContent": ["import {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  unref,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  arrow,\n  autoUpdate,\n  computePosition,\n  detectOverflow,\n  flip,\n  offset as offsetMiddelware,\n  shift,\n} from '@floating-ui/dom'\nimport {\n  isArray,\n  isClient,\n  isFunction,\n  isString,\n  keysOf,\n} from '@element-plus/utils'\n\nimport type {\n  CSSProperties,\n  Component,\n  InjectionKey,\n  Ref,\n  SetupContext,\n} from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { PosInfo, TourGap, TourMask } from './types'\nimport type {\n  ComputePositionReturn,\n  Middleware,\n  Placement,\n  Strategy,\n  VirtualElement,\n} from '@floating-ui/dom'\nimport type { TourStepProps } from './step'\n\nexport const useTarget = (\n  target: Ref<\n    string | HTMLElement | (() => HTMLElement | null) | null | undefined\n  >,\n  open: Ref<boolean>,\n  gap: Ref<TourGap>,\n  mergedMask: Ref<TourMask>,\n  scrollIntoViewOptions: Ref<boolean | ScrollIntoViewOptions>\n) => {\n  const posInfo: Ref<PosInfo | null> = ref(null)\n\n  const getTargetEl = () => {\n    let targetEl: HTMLElement | null | undefined\n    if (isString(target.value)) {\n      targetEl = document.querySelector<HTMLElement>(target.value)\n    } else if (isFunction(target.value)) {\n      targetEl = target.value()\n    } else {\n      targetEl = target.value\n    }\n    return targetEl\n  }\n\n  const updatePosInfo = () => {\n    const targetEl = getTargetEl()\n    if (!targetEl || !open.value) {\n      posInfo.value = null\n      return\n    }\n    if (!isInViewPort(targetEl)) {\n      targetEl.scrollIntoView(scrollIntoViewOptions.value)\n    }\n    const { left, top, width, height } = targetEl.getBoundingClientRect()\n    posInfo.value = {\n      left,\n      top,\n      width,\n      height,\n      radius: 0,\n    }\n  }\n\n  onMounted(() => {\n    watch(\n      [open, target],\n      () => {\n        updatePosInfo()\n      },\n      {\n        immediate: true,\n      }\n    )\n    window.addEventListener('resize', updatePosInfo)\n  })\n\n  onBeforeUnmount(() => {\n    window.removeEventListener('resize', updatePosInfo)\n  })\n\n  const getGapOffset = (index: number) =>\n    (isArray(gap.value.offset) ? gap.value.offset[index] : gap.value.offset) ??\n    6\n\n  const mergedPosInfo = computed(() => {\n    if (!posInfo.value) return posInfo.value\n\n    const gapOffsetX = getGapOffset(0)\n    const gapOffsetY = getGapOffset(1)\n    const gapRadius = gap.value?.radius || 2\n\n    return {\n      left: posInfo.value.left - gapOffsetX,\n      top: posInfo.value.top - gapOffsetY,\n      width: posInfo.value.width + gapOffsetX * 2,\n      height: posInfo.value.height + gapOffsetY * 2,\n      radius: gapRadius,\n    }\n  })\n\n  const triggerTarget = computed(() => {\n    const targetEl = getTargetEl()\n    if (!mergedMask.value || !targetEl || !window.DOMRect) {\n      return targetEl || undefined\n    }\n\n    return {\n      getBoundingClientRect() {\n        return window.DOMRect.fromRect({\n          width: mergedPosInfo.value?.width || 0,\n          height: mergedPosInfo.value?.height || 0,\n          x: mergedPosInfo.value?.left || 0,\n          y: mergedPosInfo.value?.top || 0,\n        })\n      },\n    }\n  })\n\n  return {\n    mergedPosInfo,\n    triggerTarget,\n  }\n}\n\nexport interface TourContext {\n  currentStep: Ref<TourStepProps | undefined>\n  current: Ref<number>\n  total: Ref<number>\n  showClose: Ref<boolean>\n  closeIcon: Ref<string | Component>\n  mergedType: Ref<'default' | 'primary' | undefined>\n  ns: UseNamespaceReturn\n  slots: SetupContext['slots']\n  updateModelValue(modelValue: boolean): void\n  onClose(): void\n  onFinish(): void\n  onChange(): void\n}\n\nexport const tourKey: InjectionKey<TourContext> = Symbol('ElTour')\n\nfunction isInViewPort(element: HTMLElement) {\n  const viewWidth = window.innerWidth || document.documentElement.clientWidth\n  const viewHeight = window.innerHeight || document.documentElement.clientHeight\n  const { top, right, bottom, left } = element.getBoundingClientRect()\n\n  return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight\n}\n\nexport const useFloating = (\n  referenceRef: Ref<HTMLElement | VirtualElement | null>,\n  contentRef: Ref<HTMLElement | null>,\n  arrowRef: Ref<HTMLElement | null>,\n  placement: Ref<Placement | undefined>,\n  strategy: Ref<Strategy>,\n  offset: Ref<number>,\n  zIndex: Ref<number>,\n  showArrow: Ref<boolean>\n) => {\n  const x = ref<number>()\n  const y = ref<number>()\n  const middlewareData = ref<ComputePositionReturn['middlewareData']>({})\n\n  const states = {\n    x,\n    y,\n    placement,\n    strategy,\n    middlewareData,\n  } as const\n\n  const middleware = computed(() => {\n    const _middleware: Middleware[] = [\n      offsetMiddelware(unref(offset)),\n      flip(),\n      shift(),\n      overflowMiddleware(),\n    ]\n\n    if (unref(showArrow) && unref(arrowRef)) {\n      _middleware.push(\n        arrow({\n          element: unref(arrowRef)!,\n        })\n      )\n    }\n    return _middleware\n  })\n\n  const update = async () => {\n    if (!isClient) return\n\n    const referenceEl = unref(referenceRef)\n    const contentEl = unref(contentRef)\n    if (!referenceEl || !contentEl) return\n\n    const data = await computePosition(referenceEl, contentEl, {\n      placement: unref(placement),\n      strategy: unref(strategy),\n      middleware: unref(middleware),\n    })\n\n    keysOf(states).forEach((key) => {\n      states[key].value = data[key]\n    })\n  }\n\n  const contentStyle = computed<CSSProperties>(() => {\n    if (!unref(referenceRef)) {\n      return {\n        position: 'fixed',\n        top: '50%',\n        left: '50%',\n        transform: 'translate3d(-50%, -50%, 0)',\n        maxWidth: '100vw',\n        zIndex: unref(zIndex),\n      }\n    }\n\n    const { overflow } = unref(middlewareData)\n\n    return {\n      position: unref(strategy),\n      zIndex: unref(zIndex),\n      top: unref(y) != null ? `${unref(y)}px` : '',\n      left: unref(x) != null ? `${unref(x)}px` : '',\n      maxWidth: overflow?.maxWidth ? `${overflow?.maxWidth}px` : '',\n    }\n  })\n\n  const arrowStyle = computed<CSSProperties>(() => {\n    if (!unref(showArrow)) return {}\n\n    const { arrow } = unref(middlewareData)\n    return {\n      left: arrow?.x != null ? `${arrow?.x}px` : '',\n      top: arrow?.y != null ? `${arrow?.y}px` : '',\n    }\n  })\n\n  let cleanup: any\n  onMounted(() => {\n    const referenceEl = unref(referenceRef)\n    const contentEl = unref(contentRef)\n    if (referenceEl && contentEl) {\n      cleanup = autoUpdate(referenceEl, contentEl, update)\n    }\n\n    watchEffect(() => {\n      update()\n    })\n  })\n\n  onBeforeUnmount(() => {\n    cleanup && cleanup()\n  })\n\n  return {\n    update,\n    contentStyle,\n    arrowStyle,\n  }\n}\n\nconst overflowMiddleware = (): Middleware => {\n  return {\n    name: 'overflow',\n    async fn(state) {\n      const overflow = await detectOverflow(state)\n      let overWidth = 0\n      if (overflow.left > 0) overWidth = overflow.left\n      if (overflow.right > 0) overWidth = overflow.right\n      const floatingWidth = state.rects.floating.width\n      return {\n        data: {\n          maxWidth: floatingWidth - overWidth,\n        },\n      }\n    },\n  }\n}\n"], "names": ["offset", "offsetMiddelware"], "mappings": ";;;;;;AAyBY,MAAC,SAAS,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,qBAAqB,KAAK;AACnF,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5B,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,QAAQ,CAAC;AACjB,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AAChC,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtD,KAAK,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AACzC,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AAClC,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;AACjC,MAAM,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;AAC1E,IAAI,OAAO,CAAC,KAAK,GAAG;AACpB,MAAM,IAAI;AACV,MAAM,GAAG;AACT,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,MAAM;AAChC,MAAM,aAAa,EAAE,CAAC;AACtB,KAAK,EAAE;AACP,MAAM,SAAS,EAAE,IAAI;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AACrD,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AACxD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;AAC1G,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM;AACvC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK;AACtB,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC;AAC3B,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC,IAAI,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;AAC3E,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU;AAC3C,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU;AACzC,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC;AACjD,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC;AACnD,MAAM,MAAM,EAAE,SAAS;AACvB,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM;AACvC,IAAI,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3D,MAAM,OAAO,QAAQ,IAAI,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO;AACX,MAAM,qBAAqB,GAAG;AAC9B,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,QAAQ,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACvC,UAAU,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,CAAC;AAC9E,UAAU,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,CAAC;AAChF,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC;AACzE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;AACxE,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,EAAE;AACxC,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC;AAC9E,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC;AACjF,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;AACvE,EAAE,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,SAAS,IAAI,MAAM,IAAI,UAAU,CAAC;AAC7E,CAAC;AACW,MAAC,WAAW,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAEA,QAAM,EAAE,MAAM,EAAE,SAAS,KAAK;AACnH,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,EAAE,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,MAAM,WAAW,GAAG;AACxB,MAAMC,MAAgB,CAAC,KAAK,CAACD,QAAM,CAAC,CAAC;AACrC,MAAM,IAAI,EAAE;AACZ,MAAM,KAAK,EAAE;AACb,MAAM,kBAAkB,EAAE;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE;AAC7C,MAAM,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;AAC7B,QAAQ,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC;AAChC,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,MAAM,GAAG,YAAY;AAC7B,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC5C,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS;AAClC,MAAM,OAAO;AACb,IAAI,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;AAC/D,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;AACjC,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC;AAC/B,MAAM,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACpC,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC9B,MAAM,OAAO;AACb,QAAQ,QAAQ,EAAE,OAAO;AACzB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,SAAS,EAAE,4BAA4B;AAC/C,QAAQ,QAAQ,EAAE,OAAO;AACzB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;AAC7B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;AAC/C,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC;AAC/B,MAAM,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;AAC3B,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;AAClD,MAAM,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;AACnD,MAAM,QAAQ,EAAE,CAAC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,IAAI,CAAC,EAAE,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE;AAC3H,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;AACzB,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;AACpD,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;AACzG,MAAM,GAAG,EAAE,CAAC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE;AACxG,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC5C,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,IAAI,WAAW,IAAI,SAAS,EAAE;AAClC,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,WAAW,CAAC,MAAM;AACtB,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;AACzB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,MAAM;AACV,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ,EAAE;AACF,MAAM,kBAAkB,GAAG,MAAM;AACjC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE;AACpB,MAAM,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAC;AACnD,MAAM,IAAI,SAAS,GAAG,CAAC,CAAC;AACxB,MAAM,IAAI,QAAQ,CAAC,IAAI,GAAG,CAAC;AAC3B,QAAQ,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC;AAClC,MAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC;AAC5B,QAAQ,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnC,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;AACvD,MAAM,OAAO;AACb,QAAQ,IAAI,EAAE;AACd,UAAU,QAAQ,EAAE,aAAa,GAAG,SAAS;AAC7C,SAAS;AACT,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;;;;"}