{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/collapse/index.ts"], "sourcesContent": ["import { withInstall, withN<PERSON>Install } from '@element-plus/utils'\nimport Collapse from './src/collapse.vue'\nimport CollapseItem from './src/collapse-item.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCollapse: SFCWithInstall<typeof Collapse> & {\n  CollapseItem: typeof CollapseItem\n} = withInstall(Collapse, {\n  CollapseItem,\n})\nexport default ElCollapse\nexport const ElCollapseItem: SFCWithInstall<typeof CollapseItem> =\n  withNoopInstall(CollapseItem)\n\nexport * from './src/collapse'\nexport * from './src/collapse-item'\nexport * from './src/constants'\nexport type { CollapseInstance, CollapseItemInstance } from './src/instance'\n"], "names": ["withInstall", "Collapse", "CollapseItem", "withNoopInstall"], "mappings": ";;;;;;;;;;;AAGY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ,EAAE;AAChD,gBAAEC,yBAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAGC,uBAAe,CAACD,yBAAY;;;;;;;;;;;"}