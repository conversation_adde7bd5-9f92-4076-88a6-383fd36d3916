{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-focus-controller/index.ts"], "sourcesContent": ["import {\n  getCurrentInstance,\n  onMounted,\n  ref,\n  shallowRef,\n  unref,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isElement, isFocusable, isFunction } from '@element-plus/utils'\n\nimport type { ShallowRef } from 'vue'\nimport type { MaybeRef } from '@vueuse/core'\n\ninterface UseFocusControllerOptions {\n  disabled?: MaybeRef<boolean>\n  /**\n   * return true to cancel focus\n   * @param event FocusEvent\n   */\n  beforeFocus?: (event: FocusEvent) => boolean | undefined\n  afterFocus?: () => void\n  /**\n   * return true to cancel blur\n   * @param event FocusEvent\n   */\n  beforeBlur?: (event: FocusEvent) => boolean | undefined\n  afterBlur?: () => void\n}\n\nexport function useFocusController<T extends { focus: () => void }>(\n  target: ShallowRef<T | undefined>,\n  {\n    disabled,\n    beforeFocus,\n    afterFocus,\n    beforeBlur,\n    afterBlur,\n  }: UseFocusControllerOptions = {}\n) {\n  const instance = getCurrentInstance()!\n  const { emit } = instance\n  const wrapperRef = shallowRef<HTMLElement>()\n  const isFocused = ref(false)\n\n  const handleFocus = (event: FocusEvent) => {\n    const cancelFocus = isFunction(beforeFocus) ? beforeFocus(event) : false\n    if (unref(disabled) || isFocused.value || cancelFocus) return\n\n    isFocused.value = true\n    emit('focus', event)\n    afterFocus?.()\n  }\n\n  const handleBlur = (event: FocusEvent) => {\n    const cancelBlur = isFunction(beforeBlur) ? beforeBlur(event) : false\n    if (\n      unref(disabled) ||\n      (event.relatedTarget &&\n        wrapperRef.value?.contains(event.relatedTarget as Node)) ||\n      cancelBlur\n    )\n      return\n\n    isFocused.value = false\n    emit('blur', event)\n    afterBlur?.()\n  }\n\n  const handleClick = (event: Event) => {\n    if (\n      unref(disabled) ||\n      isFocusable(event.target as HTMLElement) ||\n      (wrapperRef.value?.contains(document.activeElement) &&\n        wrapperRef.value !== document.activeElement)\n    )\n      return\n\n    target.value?.focus()\n  }\n\n  watch([wrapperRef, () => unref(disabled)], ([el, disabled]) => {\n    if (!el) return\n    if (disabled) {\n      el.removeAttribute('tabindex')\n    } else {\n      el.setAttribute('tabindex', '-1')\n    }\n  })\n\n  useEventListener(wrapperRef, 'focus', handleFocus, true)\n  useEventListener(wrapperRef, 'blur', handleBlur, true)\n  useEventListener(wrapperRef, 'click', handleClick, true)\n\n  // only for test\n  if (process.env.NODE_ENV === 'test') {\n    onMounted(() => {\n      const targetEl = isElement(target.value)\n        ? target.value\n        : document.querySelector('input,textarea')\n\n      if (targetEl) {\n        useEventListener(targetEl, 'focus', handleFocus, true)\n        useEventListener(targetEl, 'blur', handleBlur, true)\n      }\n    })\n  }\n\n  return {\n    isFocused,\n    /** Avoid using wrapperRef and handleFocus/handleBlur together */\n    wrapperRef,\n    handleFocus,\n    handleBlur,\n  }\n}\n"], "names": ["getCurrentInstance", "shallowRef", "ref", "isFunction", "unref", "isFocusable", "watch", "useEventListener", "onMounted", "isElement"], "mappings": ";;;;;;;;;;AAUO,SAAS,kBAAkB,CAAC,MAAM,EAAE;AAC3C,EAAE,QAAQ;AACV,EAAE,WAAW;AACb,EAAE,UAAU;AACZ,EAAE,UAAU;AACZ,EAAE,SAAS;AACX,CAAC,GAAG,EAAE,EAAE;AACR,EAAE,MAAM,QAAQ,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;AAC5B,EAAE,MAAM,UAAU,GAAGC,cAAU,EAAE,CAAC;AAClC,EAAE,MAAM,SAAS,GAAGC,OAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,MAAM,WAAW,GAAGC,iBAAU,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC7E,IAAI,IAAIC,SAAK,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,IAAI,WAAW;AACzD,MAAM,OAAO;AACb,IAAI,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACzB,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;AAC/C,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,KAAK,KAAK;AAChC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,UAAU,GAAGD,iBAAU,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC1E,IAAI,IAAIC,SAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,UAAU;AAC7I,MAAM,OAAO;AACb,IAAI,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxB,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC;AAC7C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAIA,SAAK,CAAC,QAAQ,CAAC,IAAIC,gBAAW,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,UAAU,CAAC,KAAK,KAAK,QAAQ,CAAC,aAAa;AACvL,MAAM,OAAO;AACb,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACtD,GAAG,CAAC;AACJ,EAAEC,SAAK,CAAC,CAAC,UAAU,EAAE,MAAMF,SAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK;AAClE,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,OAAO;AACb,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACxC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAEG,qBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AAC3D,EAAEA,qBAAgB,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AACzD,EAAEA,qBAAgB,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AAC3D,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE;AACvC,IAAIC,aAAS,CAAC,MAAM;AACpB,MAAM,MAAM,QAAQ,GAAGC,eAAS,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;AACzG,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQF,qBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AAC/D,QAAQA,qBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AAC7D,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}