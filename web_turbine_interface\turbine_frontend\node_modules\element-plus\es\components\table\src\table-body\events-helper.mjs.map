{"version": 3, "file": "events-helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/events-helper.ts"], "sourcesContent": ["import { h, inject, ref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { addClass, hasClass, removeClass } from '@element-plus/utils'\nimport {\n  createTablePopper,\n  getCell,\n  getColumnByCell,\n  removePopper,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\n\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\nimport type { DefaultRow } from '../table/defaults'\n\nfunction isGreaterThan(a: number, b: number, epsilon = 0.03) {\n  return a - b > epsilon\n}\n\nfunction useEvents<T extends DefaultRow>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const tooltipContent = ref('')\n  const tooltipTrigger = ref(h('div'))\n  const handleEvent = (event: Event, row: T, name: string) => {\n    const table = parent\n    const cell = getCell(event)\n    let column: TableColumnCtx<T> | null = null\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store?.states.columns.value ?? [],\n        },\n        cell,\n        namespace\n      )\n      if (column) {\n        table?.emit(`cell-${name}`, row, column, cell, event)\n      }\n    }\n    table?.emit(`row-${name}`, row, column, event)\n  }\n  const handleDoubleClick = (event: Event, row: T) => {\n    handleEvent(event, row, 'dblclick')\n  }\n  const handleClick = (event: Event, row: T) => {\n    props.store?.commit('setCurrentRow', row)\n    handleEvent(event, row, 'click')\n  }\n  const handleContextMenu = (event: Event, row: T) => {\n    handleEvent(event, row, 'contextmenu')\n  }\n  const handleMouseEnter = debounce((index: number) => {\n    props.store?.commit('setHoverRow', index)\n  }, 30)\n  const handleMouseLeave = debounce(() => {\n    props.store?.commit('setHoverRow', null)\n  }, 30)\n  const getPadding = (el: HTMLElement) => {\n    const style = window.getComputedStyle(el, null)\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom,\n    }\n  }\n\n  const toggleRowClassByCell = (\n    rowSpan: number,\n    event: MouseEvent,\n    toggle: (el: Element, cls: string) => void\n  ) => {\n    let node: Node | null | undefined = (event?.target as Element | null)\n      ?.parentNode\n    while (rowSpan > 1) {\n      node = node?.nextSibling\n      if (!node || node.nodeName !== 'TR') break\n      toggle(node as Element, 'hover-row hover-fixed-row')\n      rowSpan--\n    }\n  }\n\n  const handleCellMouseEnter = (\n    event: MouseEvent,\n    row: T,\n    tooltipOptions: TableOverflowTooltipOptions\n  ) => {\n    if (!parent) return\n    const table = parent\n    const cell = getCell(event)\n    const namespace = table?.vnode.el?.dataset.prefix\n    let column: TableColumnCtx<T> | null = null\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store?.states.columns.value ?? [],\n        },\n        cell,\n        namespace\n      )\n      if (!column) {\n        return\n      }\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass)\n      }\n      const hoverState = (table.hoverState = {\n        cell,\n        column: column as any,\n        row,\n      })\n      table?.emit(\n        'cell-mouse-enter',\n        hoverState.row,\n        hoverState.column,\n        hoverState.cell,\n        event\n      )\n    }\n\n    if (!tooltipOptions) {\n      return\n    }\n\n    // 判断是否text-overflow, 如果是就显示tooltip\n    const cellChild = (event.target as HTMLElement).querySelector(\n      '.cell'\n    ) as HTMLElement\n    if (\n      !(\n        hasClass(cellChild, `${namespace}-tooltip`) &&\n        cellChild.childNodes.length\n      )\n    ) {\n      return\n    }\n    // use range width instead of scrollWidth to determine whether the text is overflowing\n    // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3\n    const range = document.createRange()\n    range.setStart(cellChild, 0)\n    range.setEnd(cellChild, cellChild.childNodes.length)\n    /** detail: https://github.com/element-plus/element-plus/issues/10790\n     *  What went wrong?\n     *  UI > Browser > Zoom, In Blink/WebKit, getBoundingClientRect() sometimes returns inexact values, probably due to lost precision during internal calculations. In the example above:\n     *    - Expected: 188\n     *    - Actual: 188.00000762939453\n     */\n    const { width: rangeWidth, height: rangeHeight } =\n      range.getBoundingClientRect()\n    const { width: cellChildWidth, height: cellChildHeight } =\n      cellChild.getBoundingClientRect()\n\n    const { top, left, right, bottom } = getPadding(cellChild)\n    const horizontalPadding = left + right\n    const verticalPadding = top + bottom\n    if (\n      isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) ||\n      isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) ||\n      // When using a high-resolution screen, it is possible that a returns cellChild.scrollWidth value of 1921 and\n      // cellChildWidth returns a value of 1920.994140625. #16856 #16673\n      isGreaterThan(cellChild.scrollWidth, cellChildWidth)\n    ) {\n      createTablePopper(\n        tooltipOptions,\n        (cell?.innerText || cell?.textContent) ?? '',\n        row,\n        column,\n        cell,\n        table\n      )\n    } else if (removePopper?.trigger === cell) {\n      removePopper?.()\n    }\n  }\n  const handleCellMouseLeave = (event: MouseEvent) => {\n    const cell = getCell(event)\n    if (!cell) return\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass)\n    }\n    const oldHoverState = parent?.hoverState\n    parent?.emit(\n      'cell-mouse-leave',\n      oldHoverState?.row,\n      oldHoverState?.column,\n      oldHoverState?.cell,\n      event\n    )\n  }\n\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useEvents\n"], "names": [], "mappings": ";;;;;;AAUA,SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE;AAC7C,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;AACzB,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACvC,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,KAAK;AAC5C,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACtB,IAAI,MAAM,SAAS,GAAG,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1G,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,GAAG,eAAe,CAAC;AAC/B,QAAQ,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AACvG,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1B,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtF,OAAO;AACP,KAAK;AACL,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3E,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC5C,IAAI,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AACxC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AACtC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AAC1E,IAAI,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC5C,IAAI,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,CAAC,KAAK,KAAK;AAC/C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC1E,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM;AAC1C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACzE,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,EAAE,MAAM,UAAU,GAAG,CAAC,EAAE,KAAK;AAC7B,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACtE,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACxE,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK;AAC3D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;AAC7F,IAAI,OAAO,OAAO,GAAG,CAAC,EAAE;AACxB,MAAM,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AACtD,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI;AACzC,QAAQ,MAAM;AACd,MAAM,MAAM,CAAC,IAAI,EAAE,2BAA2B,CAAC,CAAC;AAChD,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,cAAc,KAAK;AAC/D,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,OAAO;AACb,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,MAAM,SAAS,GAAG,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1G,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,GAAG,eAAe,CAAC;AAC/B,QAAQ,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE;AACvG,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1B,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;AAC5B,QAAQ,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC5D,OAAO;AACP,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,GAAG;AAC5C,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,GAAG;AACX,OAAO,CAAC;AACR,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzH,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1D,IAAI,IAAI,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AACvF,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AACzC,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACjC,IAAI,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACzD,IAAI,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC;AACrF,IAAI,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;AACjG,IAAI,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,MAAM,iBAAiB,GAAG,IAAI,GAAG,KAAK,CAAC;AAC3C,IAAI,MAAM,eAAe,GAAG,GAAG,GAAG,MAAM,CAAC;AACzC,IAAI,IAAI,aAAa,CAAC,UAAU,GAAG,iBAAiB,EAAE,cAAc,CAAC,IAAI,aAAa,CAAC,WAAW,GAAG,eAAe,EAAE,eAAe,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE;AAChM,MAAM,iBAAiB,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,MAAM,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACnL,KAAK,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,MAAM,IAAI,EAAE;AAC7E,MAAM,CAAC,EAAE,GAAG,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC;AAClD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC1C,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;AAC1B,MAAM,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC;AACtE,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACrO,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}