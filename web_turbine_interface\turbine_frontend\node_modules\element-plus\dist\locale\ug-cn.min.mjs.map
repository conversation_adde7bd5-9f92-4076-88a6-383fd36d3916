{"version": 3, "file": "ug-cn.min.mjs", "sources": ["../../../../packages/locale/lang/ug-cn.ts"], "sourcesContent": ["export default {\n  name: 'ug-cn',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'جەزملەش',\n      clear: 'قۇرۇقداش',\n    },\n    datepicker: {\n      now: 'ھازىرقى ۋاقىت',\n      today: 'بۈگۈن',\n      cancel: 'بىكار قىلىش',\n      clear: 'قۇرۇقداش',\n      confirm: 'جەزملەش',\n      selectDate: 'چىسلا تاللاڭ',\n      selectTime: 'ۋاقىت تاللاڭ',\n      startDate: 'باشلانغان چىسلا',\n      startTime: 'باشلانغان ۋاقىت',\n      endDate: 'ئاخىرلاشقان چىسلا',\n      endTime: 'ئاخىرلاشقان ۋاقىت',\n      prevYear: 'ئالدىنقى يىل',\n      nextYear: 'كىيىنكى يىل',\n      prevMonth: 'ئالدىنقى ئاي',\n      nextMonth: 'كىيىنكى ئاي',\n      year: '- يىل',\n      month1: '1-ئاي',\n      month2: '2-ئاي',\n      month3: '3-ئاي',\n      month4: '4-ئاي',\n      month5: '5-ئاي',\n      month6: '6-ئاي',\n      month7: '7-ئاي',\n      month8: '8-ئاي',\n      month9: '9-ئاي',\n      month10: '10-ئاي',\n      month11: '11-ئاي',\n      month12: '12-ئاي',\n      // week: '周次',\n      weeks: {\n        sun: 'يەكشەنبە',\n        mon: 'دۈشەنبە',\n        tue: 'سەيشەنبە',\n        wed: 'چارشەنبە',\n        thu: 'پەيشەنبە',\n        fri: 'جۈمە',\n        sat: 'شەنبە',\n      },\n      months: {\n        jan: '1-ئاي',\n        feb: '2-ئاي',\n        mar: '3-ئاي',\n        apr: '4-ئاي',\n        may: '5-ئاي',\n        jun: '6-ئاي',\n        jul: '7-ئاي',\n        aug: '8-ئاي',\n        sep: '9-ئاي',\n        oct: '10-ئاي',\n        nov: '11-ئاي',\n        dec: '12-ئاي',\n      },\n    },\n    select: {\n      loading: 'يۈكلىنىۋاتىدۇ',\n      noMatch: 'ئۇچۇر تېپىلمىدى',\n      noData: 'ئۇچۇر يوق',\n      placeholder: 'تاللاڭ',\n    },\n    mention: {\n      loading: 'يۈكلىنىۋاتىدۇ',\n    },\n    cascader: {\n      noMatch: 'ئۇچۇر تېپىلمىدى',\n      loading: 'يۈكلىنىۋاتىدۇ',\n      placeholder: 'تاللاڭ',\n      noData: 'ئۇچۇر يوق',\n    },\n    pagination: {\n      goto: 'كىيىنكى بەت',\n      pagesize: 'تال/بەت',\n      total: 'جەمئىي {total} تال',\n      pageClassifier: 'بەت',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'ئەسكەرتىش',\n      confirm: 'جەزملەش',\n      cancel: 'بىكار قىلىش',\n      error: 'كىرگۈزگەن ئۇچۇرىڭىزدا خاتالىق بار!',\n    },\n    upload: {\n      deleteTip: 'delete كۇنپكىسىنى بېسىپ ئۆچۈرەلەيسىز',\n      delete: 'ئۆچۈرۈش',\n      preview: 'رەسىمنى كۆرۈش',\n      continue: 'رەسىم يوللاش',\n    },\n    table: {\n      emptyText: 'ئۇچۇر يوق',\n      confirmFilter: 'سۈزگۈچ',\n      resetFilter: 'قايتا تولدۇرۇش',\n      clearFilter: 'ھەممە',\n      sumText: 'جەمئىي',\n    },\n    tree: {\n      emptyText: 'ئۇچۇر يوق',\n    },\n    transfer: {\n      noMatch: 'ئۇچۇر تېپىلمىدى',\n      noData: 'ئۇچۇر يوق',\n      titles: ['جەدۋەل 1', 'جەدۋەل 2'],\n      filterPlaceholder: 'ئىزدىمەكچى بولغان مەزمۇننى كىرگۈزۈڭ',\n      noCheckedFormat: 'جەمئىي {total} تۈر',\n      hasCheckedFormat: 'تاللانغىنى {checked}/{total} تۈر',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,WAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,2EAA2E,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,+DAA+D,CAAC,KAAK,CAAC,kDAAkD,CAAC,OAAO,CAAC,4CAA4C,CAAC,UAAU,CAAC,qEAAqE,CAAC,UAAU,CAAC,qEAAqE,CAAC,SAAS,CAAC,uFAAuF,CAAC,SAAS,CAAC,uFAAuF,CAAC,OAAO,CAAC,mGAAmG,CAAC,OAAO,CAAC,mGAAmG,CAAC,QAAQ,CAAC,qEAAqE,CAAC,QAAQ,CAAC,+DAA+D,CAAC,SAAS,CAAC,qEAAqE,CAAC,SAAS,CAAC,+DAA+D,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gFAAgF,CAAC,OAAO,CAAC,uFAAuF,CAAC,MAAM,CAAC,mDAAmD,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,gFAAgF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uFAAuF,CAAC,OAAO,CAAC,gFAAgF,CAAC,WAAW,CAAC,sCAAsC,CAAC,MAAM,CAAC,mDAAmD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,+DAA+D,CAAC,QAAQ,CAAC,uCAAuC,CAAC,KAAK,CAAC,iEAAiE,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,wDAAwD,CAAC,OAAO,CAAC,4CAA4C,CAAC,MAAM,CAAC,+DAA+D,CAAC,KAAK,CAAC,0LAA0L,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6KAA6K,CAAC,MAAM,CAAC,4CAA4C,CAAC,OAAO,CAAC,2EAA2E,CAAC,QAAQ,CAAC,qEAAqE,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,aAAa,CAAC,sCAAsC,CAAC,WAAW,CAAC,iFAAiF,CAAC,WAAW,CAAC,gCAAgC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,mDAAmD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uFAAuF,CAAC,MAAM,CAAC,mDAAmD,CAAC,MAAM,CAAC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,CAAC,iBAAiB,CAAC,qMAAqM,CAAC,eAAe,CAAC,iEAAiE,CAAC,gBAAgB,CAAC,mGAAmG,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}