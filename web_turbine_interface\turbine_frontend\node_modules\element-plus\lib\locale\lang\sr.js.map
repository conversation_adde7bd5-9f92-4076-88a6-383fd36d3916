{"version": 3, "file": "sr.js", "sources": ["../../../../../packages/locale/lang/sr.ts"], "sourcesContent": ["export default {\n  name: 'sr',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Поништ<PERSON>',\n    },\n    datepicker: {\n      now: 'Сада',\n      today: 'Дана<PERSON>',\n      cancel: 'От<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Бриш<PERSON>',\n      confirm: 'OK',\n      selectDate: 'Изабери датум',\n      selectTime: 'Изабери време',\n      startDate: 'Датум почетка',\n      startTime: 'Време почетка',\n      endDate: 'Датум завршетка',\n      endTime: 'Време завршетка',\n      prevYear: 'Претходна година',\n      nextYear: 'Следећа година',\n      prevMonth: 'Претходни месец',\n      nextMonth: 'Следећи месец',\n      year: 'година',\n      month1: 'јануар',\n      month2: 'фебруар',\n      month3: 'март',\n      month4: 'април',\n      month5: 'мај',\n      month6: 'јун',\n      month7: 'јул',\n      month8: 'август',\n      month9: 'септембар',\n      month10: 'октобар',\n      month11: 'новембар',\n      month12: 'децембар',\n      week: 'седмица',\n      weeks: {\n        sun: 'Нед',\n        mon: 'Пон',\n        tue: 'Уто',\n        wed: 'Сре',\n        thu: 'Чет',\n        fri: 'Пет',\n        sat: 'Суб',\n      },\n      months: {\n        jan: 'јан',\n        feb: 'феб',\n        mar: 'мар',\n        apr: 'апр',\n        may: 'мај',\n        jun: 'јун',\n        jul: 'јул',\n        aug: 'авг',\n        sep: 'сеп',\n        oct: 'окт',\n        nov: 'нов',\n        dec: 'дец',\n      },\n    },\n    select: {\n      loading: 'Учитавање',\n      noMatch: 'Нема резултата',\n      noData: 'Нема података',\n      placeholder: 'Изабери',\n    },\n    mention: {\n      loading: 'Учитавање',\n    },\n    cascader: {\n      noMatch: 'Нема резултата',\n      loading: 'Учитавање',\n      placeholder: 'Изабери',\n      noData: 'Нема података',\n    },\n    pagination: {\n      goto: 'Иди на',\n      pagesize: '/страни',\n      total: 'Укупно {total}',\n      pageClassifier: '',\n      page: 'Страна',\n      prev: 'Иди на претходну страну',\n      next: 'Иди на следећу страну',\n      currentPage: 'страна {pager}',\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Порука',\n      confirm: 'OK',\n      cancel: 'Откажи',\n      error: 'Неисправан унос',\n    },\n    upload: {\n      deleteTip: 'притисни БРИШИ да обришеш',\n      delete: 'Бриши',\n      preview: 'Види',\n      continue: 'Настави',\n    },\n    table: {\n      emptyText: 'Нема података',\n      confirmFilter: 'Потврди',\n      resetFilter: 'Ресетуј',\n      clearFilter: 'Све',\n      sumText: 'Збир',\n    },\n    tree: {\n      emptyText: 'Нема података',\n    },\n    transfer: {\n      noMatch: 'Нема резултата',\n      noData: 'Нема података',\n      titles: ['Листа 1', 'Листа 2'], // to be translated\n      filterPlaceholder: 'Унеси кључну реч',\n      noCheckedFormat: '{total} ставки',\n      hasCheckedFormat: '{checked}/{total} обележених',\n    },\n    image: {\n      error: 'НЕУСПЕШНО',\n    },\n    pageHeader: {\n      title: 'Назад',\n    },\n    popconfirm: {\n      confirmButtonText: 'Да',\n      cancelButtonText: 'Не',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,0BAA0B;AACrC,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,2EAA2E;AAC7F,MAAM,UAAU,EAAE,2EAA2E;AAC7F,MAAM,SAAS,EAAE,2EAA2E;AAC5F,MAAM,SAAS,EAAE,2EAA2E;AAC5F,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,QAAQ,EAAE,6FAA6F;AAC7G,MAAM,QAAQ,EAAE,iFAAiF;AACjG,MAAM,SAAS,EAAE,uFAAuF;AACxG,MAAM,SAAS,EAAE,2EAA2E;AAC5F,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,wDAAwD;AACtE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,MAAM,EAAE,2EAA2E;AACzF,MAAM,WAAW,EAAE,4CAA4C;AAC/D,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,wDAAwD;AACvE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,OAAO,EAAE,wDAAwD;AACvE,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,MAAM,EAAE,2EAA2E;AACzF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,iCAAiC;AAC7C,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,KAAK,EAAE,8CAA8C;AAC3D,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,IAAI,EAAE,6HAA6H;AACzI,MAAM,IAAI,EAAE,iHAAiH;AAC7H,MAAM,WAAW,EAAE,8CAA8C;AACjE,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,sCAAsC;AACnD,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,uFAAuF;AACpG,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,yIAAyI;AAC1J,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,QAAQ,EAAE,4CAA4C;AAC5D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,2EAA2E;AAC5F,MAAM,aAAa,EAAE,4CAA4C;AACjE,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,0BAA0B;AACzC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,2EAA2E;AAC5F,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,MAAM,EAAE,2EAA2E;AACzF,MAAM,MAAM,EAAE,CAAC,kCAAkC,EAAE,kCAAkC,CAAC;AACtF,MAAM,iBAAiB,EAAE,wFAAwF;AACjH,MAAM,eAAe,EAAE,8CAA8C;AACrE,MAAM,gBAAgB,EAAE,gFAAgF;AACxG,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,wDAAwD;AACrE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,cAAc;AACvC,MAAM,gBAAgB,EAAE,cAAc;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}