{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/tabs/src/constants.ts"], "sourcesContent": ["import type {\n  ComputedRef,\n  Injection<PERSON><PERSON>,\n  Ref,\n  Slots,\n  UnwrapRef,\n  VNode,\n} from 'vue'\nimport type { TabsProps } from './tabs'\nimport type { TabPaneProps } from './tab-pane'\nimport type { TabNavInstance } from './tab-nav'\n\nexport type TabPaneName = string | number\n\nexport type TabsPaneContext = UnwrapRef<{\n  uid: number\n  getVnode: () => VNode\n  slots: Slots\n  props: TabPaneProps\n  paneName: ComputedRef<TabPaneName | undefined>\n  active: ComputedRef<boolean>\n  index: Ref<string | undefined>\n  isClosable: ComputedRef<boolean>\n  isFocusInsidePane: () => boolean | undefined\n}>\n\nexport interface TabsRootContext {\n  props: TabsProps\n  currentName: Ref<TabPaneName>\n  registerPane: (pane: TabsPaneContext) => void\n  unregisterPane: (pane: TabsPaneContext) => void\n  nav$: Ref<TabNavInstance | undefined>\n}\n\nexport const tabsRootContextKey: InjectionKey<TabsRootContext> =\n  Symbol('tabsRootContextKey')\n"], "names": [], "mappings": "AAAY,MAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB;;;;"}