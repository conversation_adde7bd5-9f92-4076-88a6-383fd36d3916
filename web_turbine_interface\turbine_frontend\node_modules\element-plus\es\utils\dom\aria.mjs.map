{"version": 3, "file": "aria.mjs", "sources": ["../../../../../packages/utils/dom/aria.ts"], "sourcesContent": ["const FOCUSABLE_ELEMENT_SELECTORS = `a[href],button:not([disabled]),button:not([hidden]),:not([tabindex=\"-1\"]),input:not([disabled]),input:not([type=\"hidden\"]),select:not([disabled]),textarea:not([disabled])`\n\n/**\n * Determine if the testing element is visible on screen no matter if its on the viewport or not\n */\nexport const isVisible = (element: HTMLElement) => {\n  if (process.env.NODE_ENV === 'test') return true\n  const computed = getComputedStyle(element)\n  // element.offsetParent won't work on fix positioned\n  // WARNING: potential issue here, going to need some expert advices on this issue\n  return computed.position === 'fixed' ? false : element.offsetParent !== null\n}\n\nexport const obtainAllFocusableElements = (\n  element: HTMLElement\n): HTMLElement[] => {\n  return Array.from(\n    element.querySelectorAll<HTMLElement>(FOCUSABLE_ELEMENT_SELECTORS)\n  ).filter((item: HTMLElement) => isFocusable(item) && isVisible(item))\n}\n\n/**\n * @desc Determine if target element is focusable\n * @param element {HTMLElement}\n * @returns {Boolean} true if it is focusable\n */\nexport const isFocusable = (element: HTMLElement): boolean => {\n  if (\n    element.tabIndex > 0 ||\n    (element.tabIndex === 0 && element.getAttribute('tabIndex') !== null)\n  ) {\n    return true\n  }\n  if (\n    element.tabIndex < 0 ||\n    element.hasAttribute('disabled') ||\n    element.getAttribute('aria-disabled') === 'true'\n  ) {\n    return false\n  }\n\n  switch (element.nodeName) {\n    case 'A': {\n      // casting current element to Specific HTMLElement in order to be more type precise\n      return (\n        !!(element as HTMLAnchorElement).href &&\n        (element as HTMLAnchorElement).rel !== 'ignore'\n      )\n    }\n    case 'INPUT': {\n      return !(\n        (element as HTMLInputElement).type === 'hidden' ||\n        (element as HTMLInputElement).type === 'file'\n      )\n    }\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA': {\n      return true\n    }\n    default: {\n      return false\n    }\n  }\n}\n\n/**\n * @desc Set Attempt to set focus on the current node.\n * @param element\n *          The node to attempt to focus on.\n * @returns\n *  true if element is focused.\n */\nexport const attemptFocus = (element: HTMLElement): boolean => {\n  if (!isFocusable(element)) {\n    return false\n  }\n  // Remove the old try catch block since there will be no error to be thrown\n  element.focus?.()\n  return document.activeElement === element\n}\n\n/**\n * Trigger an event\n * mouseenter, mouseleave, mouseover, keyup, change, click, etc.\n * @param  {HTMLElement} elm\n * @param  {String} name\n * @param  {*} opts\n */\nexport const triggerEvent = function (\n  elm: HTMLElement,\n  name: string,\n  ...opts: Array<boolean>\n): HTMLElement {\n  let eventName: string\n\n  if (name.includes('mouse') || name.includes('click')) {\n    eventName = 'MouseEvents'\n  } else if (name.includes('key')) {\n    eventName = 'KeyboardEvent'\n  } else {\n    eventName = 'HTMLEvents'\n  }\n  const evt = document.createEvent(eventName)\n\n  evt.initEvent(name, ...opts)\n  elm.dispatchEvent(evt)\n  return elm\n}\n\nexport const isLeaf = (el: HTMLElement) => !el.getAttribute('aria-owns')\n\nexport const getSibling = (\n  el: HTMLElement,\n  distance: number,\n  elClass: string\n) => {\n  const { parentNode } = el\n  if (!parentNode) return null\n  const siblings = parentNode.querySelectorAll(elClass)\n  const index = Array.prototype.indexOf.call(siblings, el)\n  return siblings[index + distance] || null\n}\n\nexport const focusNode = (el: HTMLElement) => {\n  if (!el) return\n  el.focus()\n  !isLeaf(el) && el.click()\n}\n"], "names": [], "mappings": "AAAA,MAAM,2BAA2B,GAAG,CAAC,0KAA0K,CAAC,CAAC;AACrM,MAAC,SAAS,GAAG,CAAC,OAAO,KAAK;AACtC,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;AACrC,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC7C,EAAE,OAAO,QAAQ,CAAC,QAAQ,KAAK,OAAO,GAAG,KAAK,GAAG,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC;AAC/E,EAAE;AACU,MAAC,0BAA0B,GAAG,CAAC,OAAO,KAAK;AACvD,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAClI,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,OAAO,KAAK;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;AACnG,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;AACpH,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,QAAQ,OAAO,CAAC,QAAQ;AAC1B,IAAI,KAAK,GAAG,EAAE;AACd,MAAM,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,KAAK,QAAQ,CAAC;AACxD,KAAK;AACL,IAAI,KAAK,OAAO,EAAE;AAClB,MAAM,OAAO,EAAE,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,KAAK,QAAQ,CAAC;AAClB,IAAI,KAAK,QAAQ,CAAC;AAClB,IAAI,KAAK,UAAU,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,SAAS;AACb,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,OAAO,KAAK;AACzC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;AAC7B,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3D,EAAE,OAAO,QAAQ,CAAC,aAAa,KAAK,OAAO,CAAC;AAC5C,EAAE;AACU,MAAC,YAAY,GAAG,SAAS,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE;AACzD,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACxD,IAAI,SAAS,GAAG,aAAa,CAAC;AAC9B,GAAG,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnC,IAAI,SAAS,GAAG,eAAe,CAAC;AAChC,GAAG,MAAM;AACT,IAAI,SAAS,GAAG,YAAY,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC9C,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC/B,EAAE,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACzB,EAAE,OAAO,GAAG,CAAC;AACb,EAAE;AACU,MAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE;AAChD,MAAC,UAAU,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,KAAK;AACrD,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;AAC5B,EAAE,IAAI,CAAC,UAAU;AACjB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACxD,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC3D,EAAE,OAAO,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,IAAI,CAAC;AAC5C,EAAE;AACU,MAAC,SAAS,GAAG,CAAC,EAAE,KAAK;AACjC,EAAE,IAAI,CAAC,EAAE;AACT,IAAI,OAAO;AACX,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;AACb,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AAC5B;;;;"}