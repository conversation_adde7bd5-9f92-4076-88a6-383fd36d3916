'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var method = require('./src/method.js');
var message = require('./src/message.js');
var install = require('../../utils/vue/install.js');

const ElMessage = install.withInstallFunction(method["default"], "$message");

exports.messageDefaults = message.messageDefaults;
exports.messageEmits = message.messageEmits;
exports.messageProps = message.messageProps;
exports.messageTypes = message.messageTypes;
exports.ElMessage = ElMessage;
exports["default"] = ElMessage;
//# sourceMappingURL=index.js.map
