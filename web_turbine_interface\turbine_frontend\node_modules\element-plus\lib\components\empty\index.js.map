{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/empty/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Empty from './src/empty.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElEmpty: SFCWithInstall<typeof Empty> = withInstall(Empty)\nexport default ElEmpty\n\nexport * from './src/empty'\nexport type { EmptyInstance } from './src/instance'\n"], "names": ["withInstall", "Empty"], "mappings": ";;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;"}