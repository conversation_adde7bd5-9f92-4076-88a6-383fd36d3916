{"version": 3, "file": "tree.js", "sources": ["../../../../../../packages/components/tree/src/tree.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"el$\"\n    :class=\"[\n      ns.b(),\n      ns.is('dragging', !!dragState.draggingNode),\n      ns.is('drop-not-allow', !dragState.allowDrop),\n      ns.is('drop-inner', dragState.dropType === 'inner'),\n      { [ns.m('highlight-current')]: highlightCurrent },\n    ]\"\n    role=\"tree\"\n  >\n    <el-tree-node\n      v-for=\"child in root.childNodes\"\n      :key=\"getNodeKey(child)\"\n      :node=\"child\"\n      :props=\"props\"\n      :accordion=\"accordion\"\n      :render-after-expand=\"renderAfterExpand\"\n      :show-checkbox=\"showCheckbox\"\n      :render-content=\"renderContent\"\n      @node-expand=\"handleNodeExpand\"\n    />\n    <div v-if=\"isEmpty\" :class=\"ns.e('empty-block')\">\n      <slot name=\"empty\">\n        <span :class=\"ns.e('empty-text')\">\n          {{ emptyText ?? t('el.tree.emptyText') }}\n        </span>\n      </slot>\n    </div>\n    <div\n      v-show=\"dragState.showDropIndicator\"\n      ref=\"dropIndicator$\"\n      :class=\"ns.e('drop-indicator')\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { definePropType, iconPropType } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport { selectKey } from '@element-plus/components/select/src/token'\nimport TreeStore from './model/tree-store'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport ElTreeNode from './tree-node.vue'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { useDragNodeHandler } from './model/useDragNode'\nimport { useKeydown } from './model/useKeydown'\nimport { ROOT_TREE_INJECTION_KEY } from './tokens'\n\nimport type Node from './model/node'\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  AllowDragFunction,\n  AllowDropFunction,\n  FilterValue,\n  RenderContentFunction,\n  TreeComponentProps,\n  TreeData,\n  TreeKey,\n  TreeNodeData,\n} from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTree',\n  components: { ElTreeNode },\n  props: {\n    data: {\n      type: definePropType<TreeData>(Array),\n      default: () => [],\n    },\n    emptyText: {\n      type: String,\n    },\n    renderAfterExpand: {\n      type: Boolean,\n      default: true,\n    },\n    nodeKey: String,\n    checkStrictly: Boolean,\n    defaultExpandAll: Boolean,\n    expandOnClickNode: {\n      type: Boolean,\n      default: true,\n    },\n    checkOnClickNode: Boolean,\n    checkOnClickLeaf: {\n      type: Boolean,\n      default: true,\n    },\n    checkDescendants: Boolean,\n    autoExpandParent: {\n      type: Boolean,\n      default: true,\n    },\n    defaultCheckedKeys: Array as PropType<\n      TreeComponentProps['defaultCheckedKeys']\n    >,\n    defaultExpandedKeys: Array as PropType<\n      TreeComponentProps['defaultExpandedKeys']\n    >,\n    currentNodeKey: [String, Number] as PropType<string | number>,\n    renderContent: {\n      type: definePropType<RenderContentFunction>(Function),\n    },\n    showCheckbox: Boolean,\n    draggable: Boolean,\n    allowDrag: {\n      type: definePropType<AllowDragFunction>(Function),\n    },\n    allowDrop: {\n      type: definePropType<AllowDropFunction>(Function),\n    },\n    props: {\n      type: Object as PropType<TreeComponentProps['props']>,\n      default: () => ({\n        children: 'children',\n        label: 'label',\n        disabled: 'disabled',\n      }),\n    },\n    lazy: Boolean,\n    highlightCurrent: Boolean,\n    load: Function as PropType<TreeComponentProps['load']>,\n    filterNodeMethod: Function as PropType<\n      TreeComponentProps['filterNodeMethod']\n    >,\n    accordion: Boolean,\n    indent: {\n      type: Number,\n      default: 18,\n    },\n    icon: {\n      type: iconPropType,\n    },\n  },\n  emits: [\n    'check-change',\n    'current-change',\n    'node-click',\n    'node-contextmenu',\n    'node-collapse',\n    'node-expand',\n    'check',\n    'node-drag-start',\n    'node-drag-end',\n    'node-drop',\n    'node-drag-leave',\n    'node-drag-enter',\n    'node-drag-over',\n  ] as string[],\n  setup(props, ctx) {\n    const { t } = useLocale()\n    const ns = useNamespace('tree')\n    const selectInfo = inject(selectKey, null)\n\n    const store = ref<TreeStore>(\n      new TreeStore({\n        key: props.nodeKey,\n        data: props.data,\n        lazy: props.lazy,\n        props: props.props,\n        load: props.load,\n        currentNodeKey: props.currentNodeKey,\n        checkStrictly: props.checkStrictly,\n        checkDescendants: props.checkDescendants,\n        defaultCheckedKeys: props.defaultCheckedKeys,\n        defaultExpandedKeys: props.defaultExpandedKeys,\n        autoExpandParent: props.autoExpandParent,\n        defaultExpandAll: props.defaultExpandAll,\n        filterNodeMethod: props.filterNodeMethod,\n      })\n    )\n\n    store.value.initialize()\n\n    const root = ref<Node>(store.value.root)\n    const currentNode = ref<Node | null>(null)\n    const el$ = ref<Nullable<HTMLElement>>(null)\n    const dropIndicator$ = ref<Nullable<HTMLElement>>(null)\n\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n\n    const { dragState } = useDragNodeHandler({\n      props,\n      ctx,\n      el$,\n      dropIndicator$,\n      store,\n    })\n\n    useKeydown({ el$ }, store)\n\n    const isEmpty = computed(() => {\n      const { childNodes } = root.value\n      const hasFilteredOptions = selectInfo\n        ? (selectInfo as any).hasFilteredOptions !== 0\n        : false\n      return (\n        (!childNodes ||\n          childNodes.length === 0 ||\n          childNodes.every(({ visible }) => !visible)) &&\n        !hasFilteredOptions\n      )\n    })\n\n    watch(\n      () => props.currentNodeKey,\n      (newVal) => {\n        store.value.setCurrentNodeKey(newVal ?? null)\n      }\n    )\n\n    watch(\n      () => props.defaultCheckedKeys,\n      (newVal) => {\n        store.value.setDefaultCheckedKey(newVal ?? [])\n      }\n    )\n\n    watch(\n      () => props.defaultExpandedKeys,\n      (newVal) => {\n        store.value.setDefaultExpandedKeys(newVal ?? [])\n      }\n    )\n\n    watch(\n      () => props.data,\n      (newVal) => {\n        store.value.setData(newVal)\n      },\n      { deep: true }\n    )\n\n    watch(\n      () => props.checkStrictly,\n      (newVal) => {\n        store.value.checkStrictly = newVal\n      }\n    )\n\n    const filter = (value: FilterValue) => {\n      if (!props.filterNodeMethod)\n        throw new Error('[Tree] filterNodeMethod is required when filter')\n      store.value.filter(value)\n    }\n\n    const getNodeKey = (node: Node) => {\n      return getNodeKeyUtil(props.nodeKey, node.data)\n    }\n\n    const getNodePath = (data: TreeKey | TreeNodeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getNodePath')\n      const node = store.value.getNode(data)\n      if (!node) return []\n      const path = [node.data]\n      let parent = node.parent\n      while (parent && parent !== root.value) {\n        path.push(parent.data)\n        parent = parent.parent\n      }\n      return path.reverse()\n    }\n\n    const getCheckedNodes = (\n      leafOnly?: boolean,\n      includeHalfChecked?: boolean\n    ): TreeNodeData[] => {\n      return store.value.getCheckedNodes(leafOnly, includeHalfChecked)\n    }\n\n    const getCheckedKeys = (leafOnly?: boolean): TreeKey[] => {\n      return store.value.getCheckedKeys(leafOnly)\n    }\n\n    const getCurrentNode = () => {\n      const currentNode = store.value.getCurrentNode()\n      return currentNode ? currentNode.data : null\n    }\n\n    const getCurrentKey = (): TreeKey | null => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getCurrentKey')\n      const currentNode = getCurrentNode()\n      return currentNode ? currentNode[props.nodeKey] : null\n    }\n\n    const setCheckedNodes = (nodes: Node[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedNodes')\n      store.value.setCheckedNodes(nodes, leafOnly)\n    }\n\n    const setCheckedKeys = (keys: TreeKey[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedKeys')\n      store.value.setCheckedKeys(keys, leafOnly)\n    }\n\n    const setChecked = (\n      data: TreeKey | TreeNodeData,\n      checked: boolean,\n      deep: boolean\n    ) => {\n      store.value.setChecked(data, checked, deep)\n    }\n\n    const getHalfCheckedNodes = (): TreeNodeData[] => {\n      return store.value.getHalfCheckedNodes()\n    }\n\n    const getHalfCheckedKeys = (): TreeKey[] => {\n      return store.value.getHalfCheckedKeys()\n    }\n\n    const setCurrentNode = (node: Node, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentNode')\n\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded(node)\n        store.value.setUserCurrentNode(node, shouldAutoExpandParent)\n      })\n    }\n\n    const setCurrentKey = (key?: TreeKey, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentKey')\n\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded()\n        store.value.setCurrentNodeKey(key ?? null, shouldAutoExpandParent)\n      })\n    }\n\n    const getNode = (data: TreeKey | TreeNodeData): Node => {\n      return store.value.getNode(data)\n    }\n\n    const remove = (data: TreeNodeData | Node) => {\n      store.value.remove(data)\n    }\n\n    const append = (\n      data: TreeNodeData,\n      parentNode: TreeNodeData | TreeKey | Node\n    ) => {\n      store.value.append(data, parentNode)\n    }\n\n    const insertBefore = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertBefore(data, refNode)\n    }\n\n    const insertAfter = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertAfter(data, refNode)\n    }\n\n    const handleNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const updateKeyChildren = (key: TreeKey, data: TreeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in updateKeyChild')\n      store.value.updateChildren(key, data)\n    }\n\n    provide(ROOT_TREE_INJECTION_KEY, {\n      ctx,\n      props,\n      store,\n      root,\n      currentNode,\n      instance: getCurrentInstance(),\n    })\n\n    provide(formItemContextKey, undefined)\n\n    return {\n      ns,\n      // ref\n      store,\n      root,\n      currentNode,\n      dragState,\n      el$,\n      dropIndicator$,\n\n      // computed\n      isEmpty,\n\n      // methods\n      filter,\n      getNodeKey,\n      getNodePath,\n      getCheckedNodes,\n      getCheckedKeys,\n      getCurrentNode,\n      getCurrentKey,\n      setCheckedNodes,\n      setCheckedKeys,\n      setChecked,\n      getHalfCheckedNodes,\n      getHalfCheckedKeys,\n      setCurrentNode,\n      setCurrentKey,\n      t,\n      getNode,\n      remove,\n      append,\n      insertBefore,\n      insertAfter,\n      handleNodeExpand,\n      updateKeyChildren,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElTreeNode", "definePropType", "iconPropType", "useLocale", "useNamespace", "inject", "<PERSON><PERSON><PERSON>", "ref", "TreeStore", "useNodeExpandEventBroadcast", "useDragNodeHandler", "useKeydown", "computed", "watch", "getNodeKeyUtil", "currentNode", "handleCurrentChange", "provide", "ROOT_TREE_INJECTION_KEY", "getCurrentInstance", "formItemContextKey", "_resolveComponent", "_openBlock", "_createElementBlock", "_normalizeClass", "_Fragment", "_renderList", "_createBlock", "_renderSlot", "_createElementVNode", "_toDisplayString", "_createCommentVNode", "_withDirectives", "_vShow", "_export_sfc"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA0EA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,QAAA;AAAA,EACN,UAAA,EAAY,cAAEC,mBAAW,EAAA;AAAA,EACzB,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAA,EAAMC,uBAAyB,KAAK,CAAA;AAAA,MACpC,OAAA,EAAS,MAAM,EAAC;AAAA,KAClB;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,MAAA;AAAA,KACR;AAAA,IACA,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,OAAS,EAAA,MAAA;AAAA,IACT,aAAe,EAAA,OAAA;AAAA,IACf,gBAAkB,EAAA,OAAA;AAAA,IAClB,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA,OAAA;AAAA,IAClB,gBAAkB,EAAA;AAAA,MAChB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA,OAAA;AAAA,IAClB,gBAAkB,EAAA;AAAA,MAChB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,kBAAoB,EAAA,KAAA;AAAA,IAGpB,mBAAqB,EAAA,KAAA;AAAA,IAGrB,cAAA,EAAgB,CAAC,MAAA,EAAQ,MAAM,CAAA;AAAA,IAC/B,aAAe,EAAA;AAAA,MACb,IAAA,EAAMA,uBAAsC,QAAQ,CAAA;AAAA,KACtD;AAAA,IACA,YAAc,EAAA,OAAA;AAAA,IACd,SAAW,EAAA,OAAA;AAAA,IACX,SAAW,EAAA;AAAA,MACT,IAAA,EAAMA,uBAAkC,QAAQ,CAAA;AAAA,KAClD;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAA,EAAMA,uBAAkC,QAAQ,CAAA;AAAA,KAClD;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,SAAS,OAAO;AAAA,QACd,QAAU,EAAA,UAAA;AAAA,QACV,KAAO,EAAA,OAAA;AAAA,QACP,QAAU,EAAA,UAAA;AAAA,OACZ,CAAA;AAAA,KACF;AAAA,IACA,IAAM,EAAA,OAAA;AAAA,IACN,gBAAkB,EAAA,OAAA;AAAA,IAClB,IAAM,EAAA,QAAA;AAAA,IACN,gBAAkB,EAAA,QAAA;AAAA,IAGlB,SAAW,EAAA,OAAA;AAAA,IACX,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,MAAA;AAAA,MACN,OAAS,EAAA,EAAA;AAAA,KACX;AAAA,IACA,IAAM,EAAA;AAAA,MACJ,IAAM,EAAAC,iBAAA;AAAA,KACR;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,cAAA;AAAA,IACA,gBAAA;AAAA,IACA,YAAA;AAAA,IACA,kBAAA;AAAA,IACA,eAAA;AAAA,IACA,aAAA;AAAA,IACA,OAAA;AAAA,IACA,iBAAA;AAAA,IACA,eAAA;AAAA,IACA,WAAA;AAAA,IACA,iBAAA;AAAA,IACA,iBAAA;AAAA,IACA,gBAAA;AAAA,GACF;AAAA,EACA,KAAA,CAAM,OAAO,GAAK,EAAA;AAChB,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIC,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,UAAA,GAAaC,UAAO,CAAAC,eAAA,EAAW,IAAI,CAAA,CAAA;AAEzC,IAAA,MAAM,KAAQ,GAAAC,OAAA,CAAA,IAAAC,oBAAA,CAAA;AAAA,MACZ,KAAc,KAAA,CAAA,OAAA;AAAA,MAAA,WACD,CAAA,IAAA;AAAA,MAAA,WACC,CAAA,IAAA;AAAA,MAAA,YACA,CAAA,KAAA;AAAA,MAAA,WACC,CAAA,IAAA;AAAA,MAAA,cACD,EAAA,KAAA,CAAA,cAAA;AAAA,MAAA,oBACU,CAAA,aAAA;AAAA,MAAA,kBACD,KAAA,CAAA,gBAAA;AAAA,MAAA,oBACH,KAAM,CAAA,kBAAA;AAAA,MAAA,0BACE,CAAA,mBAAA;AAAA,MAAA,uBACL,CAAM,gBAAA;AAAA,MAAA,uBACH,CAAA,gBAAA;AAAA,MAAA,uBACA,CAAA,gBAAA;AAAA,KAAA,CAAA,CAAA,CACxB;AAAwB,IAAA,KACzB,CAAA,KAAA,CAAA,UAAA,EAAA,CAAA;AAAA,IACH,MAAA,IAAA,GAAAD,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,IAAA,MAAM,WAAiB,GAAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAEvB,IAAA,MAAM,GAAO,GAAAA,OAAA,CAAA,IAAgB,CAAA,CAAA;AAC7B,IAAM,MAAA,cAAc,UAAqB,CAAA,IAAA,CAAA,CAAA;AACzC,IAAM,MAAA,EAAA,iBAAqC,EAAA,GAAAE,uDAAA,CAAA,KAAA,CAAA,CAAA;AAC3C,IAAM,MAAA,EAAA,SAAA,EAAA,GAAAC,8BAAgD,CAAA;AAEtD,MAAA,KAAQ;AAER,MAAM,GAAA;AAAmC,MACvC,GAAA;AAAA,MACA,cAAA;AAAA,MACA,KAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACAC,qBAAA,CAAA,EAAA,GAAA,EAAA,EAAA,KAAA,CAAA,CAAA;AAAA,IACF,MAAC,OAAA,GAAAC,YAAA,CAAA,MAAA;AAED,MAAW,MAAA,EAAA,UAAS,EAAK,GAAA,IAAA,CAAA,KAAA,CAAA;AAEzB,MAAM,MAAA,kBAAyB,GAAA,UAAA,GAAA,UAAA,CAAA,kBAAA,KAAA,CAAA,GAAA,KAAA,CAAA;AAC7B,MAAM,OAAA,CAAE,CAAW,UAAA,IAAI,UAAK,CAAA,MAAA,KAAA,CAAA,IAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,OAAA,EAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,kBAAA,CAAA;AAC5B,KAAA,CAAA,CAAA;AAGA,IAAAC,SAAA,CAAA,MACI,KAAA,CAAA,cACW,EAAA,CAAA,MAAA,KAAA;AAEZ,MAEJ,KAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,IAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAAA,IAAAA,gBACc,KAAA,CAAA,kBAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACZ,KAAY,CAAA,KAAA,CAAA,oBAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,EAAA,CAAA,CAAA;AACV,KAAM,CAAA,CAAA;AAAsC,IAC9CA,SAAA,CAAA,MAAA,KAAA,CAAA,mBAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACF,KAAA,CAAA,KAAA,CAAA,sBAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,EAAA,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAAA,gBACc,KAAA,CAAA,IAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACZ,KAAY,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AACV,KAAA,EAAA,EAAA,IAAA,EAAY,IAAA,EAAA,CAAA,CAAA;AAAiC,IAC/CA,SAAA,CAAA,MAAA,KAAA,CAAA,aAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACF,KAAA,CAAA,KAAA,CAAA,aAAA,GAAA,MAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AAAA,IAAA,YACc,GAAA,CAAA,KAAA,KAAA;AAAA,MACZ,IAAY,CAAA,KAAA,CAAA,gBAAA;AACV,QAAA,MAAM,IAAM,KAAA,CAAA,iDAAmC,CAAA,CAAA;AAAA,MACjD,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAA,UAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACE,OAAYC,eAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAAA,KAAA,CACZ;AACE,IAAM,MAAA,mBAAoB,KAAA;AAAA,MAC5B,IAAA,CAAA,KAAA,CAAA,OAAA;AAAA,QACE,MAAM,IAAK,KAAA,CAAA,2CAAA,CAAA,CAAA;AAAA,MACf,MAAA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAA,IAAA,CAAA,IAAA;AAAA,eACc,EAAA,CAAA;AAAA,MACZ,MAAY,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACV,MAAA,IAAA,aAA4B,CAAA,MAAA,CAAA;AAAA,MAC9B,OAAA,MAAA,IAAA,MAAA,KAAA,IAAA,CAAA,KAAA,EAAA;AAAA,QACF,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;AAEA,QAAM,MAAA,GAAA,MAAiC,CAAA,MAAA,CAAA;AACrC,OAAA;AACE,MAAM,OAAA,IAAA,CAAI,OAAuD,EAAA,CAAA;AACnE,KAAM,CAAA;AAAkB,IAC1B,MAAA,eAAA,GAAA,CAAA,QAAA,EAAA,kBAAA,KAAA;AAEA,MAAM,OAAA,KAAA,CAAA,KAAc,CAAe,eAAA,CAAA,QAAA,EAAA,kBAAA,CAAA,CAAA;AACjC,KAAA,CAAA;AAA8C,IAChD,MAAA,cAAA,GAAA,CAAA,QAAA,KAAA;AAEA,MAAM,OAAA,KAAA,CAAA,KAAc,CAAC,cAAiC,CAAA,QAAA,CAAA,CAAA;AACpD,KAAA,CAAA;AACE,IAAM,MAAA,cAAU,GAA2C,MAAA;AAC7D,MAAA,MAAM,YAAO,GAAY,KAAA,CAAA,KAAA,CAAQ,cAAI,EAAA,CAAA;AACrC,MAAI,OAAO,YAAQ,GAAA,YAAA,CAAA,IAAA,GAAA,IAAA,CAAA;AACnB,KAAM,CAAA;AACN,IAAA,MAAI,aAAc,GAAA,MAAA;AAClB,MAAO,IAAA,CAAA,KAAA,CAAA,OAAqB;AAC1B,QAAK,MAAA,IAAK,mDAAW,CAAA,CAAA;AACrB,MAAA,MAAA,YAAgB,GAAA,cAAA,EAAA,CAAA;AAAA,MAClB,OAAA,YAAA,GAAA,YAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA;AACA,KAAA,CAAA;AAAoB,IACtB,MAAA,eAAA,GAAA,CAAA,KAAA,EAAA,QAAA,KAAA;AAEA,MAAM,IAAA,CAAA,KAAA,CAAA,OAAA;AAIJ,QAAA,MAAa,IAAA,KAAA,CAAA,+CAAkD,CAAA,CAAA;AAAA,MACjE,KAAA,CAAA,KAAA,CAAA,eAAA,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,cAAY,GAAA,CAAA,IAAA,EAAA,QAAuB,KAAA;AAAA,MAC5C,IAAA,CAAA,KAAA,CAAA,OAAA;AAEA,QAAA,8DAA6B,CAAA,CAAA;AAC3B,MAAMC,KAAAA,CAAAA,KAAAA,CAAAA,cAAoB,CAAA,IAAA,EAAM,QAAe,CAAA,CAAA;AAC/C,KAAOA,CAAAA;AAAiC,IAC1C,MAAA,UAAA,GAAA,CAAA,IAAA,EAAA,OAAA,EAAA,IAAA,KAAA;AAEA,MAAA,sBAA4C,CAAA,IAAA,EAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AAC1C,KAAA,CAAA;AACE,IAAM,MAAA,mBAAuD,GAAA,MAAA;AAC/D,MAAA,sCAAmC,EAAA,CAAA;AACnC,KAAA,CAAA;AAAkD,IACpD,MAAA,kBAAA,GAAA,MAAA;AAEA,MAAM,OAAA,KAAA,CAAA,KAAA,CAAA,kBAAyD,EAAA,CAAA;AAC7D,KAAA,CAAA;AACE,IAAM,MAAA,cAAU,GAA+C,CAAA,IAAA,EAAA,sBAAA,GAAA,IAAA,KAAA;AACjE,MAAM,IAAA,CAAA,KAAA,CAAA,OAAsB;AAAe,QAC7C,MAAA,IAAA,KAAA,CAAA,8CAAA,CAAA,CAAA;AAEA,MAAMC,wBAAA,CAAA,KAAkB,EAAA,GAAwC,CAAA,IAAA,EAAA,MAAA;AAC9D,QAAA,iBAAW,CAAA,IAAA,CAAA,CAAA;AACT,QAAM,KAAA,CAAA,wBAAwD,CAAA,IAAA,EAAA,sBAAA,CAAA,CAAA;AAChE,OAAM,CAAA,CAAA;AAAmC,KAC3C,CAAA;AAEA,IAAA,MAAM,aAAa,GAEjB,CAAA,GAAA,EAAA,sBAEG,GAAA,IAAA,KAAA;AACH,MAAA,IAAA,CAAA,KAAY,CAAA,OAAA;AAA8B,QAC5C,MAAA,IAAA,KAAA,CAAA,6CAAA,CAAA,CAAA;AAEA,MAAAA,gCAAkD,GAAA,CAAA,IAAA,EAAA,MAAA;AAChD,QAAO,iBAAY,EAAoB,CAAA;AAAA,QACzC,KAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,EAAA,sBAAA,CAAA,CAAA;AAEA,OAAA,CAAA,CAAA;AACE,KAAO,CAAA;AAA+B,IACxC,MAAA,OAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAA,OAAuB,KAAA,CAAA,KAAA,CAAA,OAAa,CAAA,IAAA,CAAA,CAAA;AAClC,KAAA,CAAA;AACE,IAAM,MAAA,MAAA,QAAU,KAA8C;AAEhE,MAAoB,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AACA,IAAM,MAAA,MAAA,GAAA,CAAM,IAAmB,EAAA,UAAA,KAAA;AAA4B,MAC7D,KAAC,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,EAAA,UAAA,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAA,MAAM,YAAgB,GAAA,CAAA,IAAgB,EAAA,OAAA,KAAA;AACpC,MAAA,KAAK,CAAM,KAAA,CAAA,YAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AACT,KAAM,CAAA;AAER,IAAoB,MAAA,WAAA,GAAA,CAAA,IAAA,EAAA,OAAW,KAAM;AACnC,MAAkB,KAAA,CAAA,KAAA,CAAA,WAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AAAiE,IAAA,MAClE,gBAAA,GAAA,CAAA,QAAA,EAAA,IAAA,EAAA,QAAA,KAAA;AAAA,MACH,iBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAM,GAAA,CAAA,IAAA,CAAA,aAAkD,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AACtD,KAAO,CAAA;AAAwB,IACjC,MAAA,iBAAA,GAAA,CAAA,GAAA,EAAA,IAAA,KAAA;AAEA,MAAM,IAAA,CAAA,KAAA,CAAA,OAAwC;AAC5C,QAAM,MAAA,IAAM,oDAAW,CAAA,CAAA;AAAA,MACzB,KAAA,CAAA,KAAA,CAAA,cAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AAIJ,IAAMC,WAAA,CAAAC,8BAA6B,EAAA;AAAA,MACrC,GAAA;AAEA,MAAM,KAAA;AAIJ,MAAM,KAAA;AAAgC,MACxC,IAAA;AAEA,MAAM,WAAA;AAIJ,MAAM,QAAA,EAAAC,sBAAkB,EAAM;AAAO,KACvC,CAAA,CAAA;AAEA,IAAAF,WAAyB,CAAAG,4BACvB,EACA,KAAA,CAAA,CAAA,CAAA;AAGA,IAAA,OAAA;AACA,MAAA,EAAA;AAAgD,MAClD,KAAA;AAEA,MAAM,IAAA;AACJ,MAAA,WAAW;AACT,MAAM,SAAA;AACR,MAAM,GAAA;AAA8B,MACtC,cAAA;AAEA,MAAA,OAAiC;AAAA,MAC/B,MAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,MACA,cAA6B;AAAA,MAC9B,aAAA;AAED,MAAA;AAEA,MAAO,cAAA;AAAA,MACL,UAAA;AAAA,MAAA,mBAAA;AAAA,MAEA,kBAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,CAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MAAA,MAAA;AAAA,MAGA,YAAA;AAAA,MAAA,WAAA;AAAA,MAGA,gBAAA;AAAA,MACA,iBAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,MAAA,uBAAA,GAAAC,oBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EACA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,IACA,GAAA,EAAA,KAAA;AAAA,IACA,KAAA,EAAAC,kBAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,YAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,IAAA,CAAA,SAAA,CAAA,SAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,SAAA,CAAA,QAAA,KAAA,OAAA,CAAA;AAAA,MACA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,IAAA,CAAA,gBAAA,EAAA;AAAA,KACA,CAAA;AAAA,IACA,IAAA,EAAA,MAAA;AAAA,GACA,EAAA;AAAA,KACAF,aAAA,CAAA,IAAA,CAAA,EAAAC,sBAAA,CAAAE,YAAA,EAAA,IAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,KAAA,KAAA;AAAA,MACA,OAAAJ,aAAA,EAAA,EAAAK,eAAA,CAAA,uBAAA,EAAA;AAAA,QACA,GAAA,EAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACF,IAAA,EAAA,KAAA;AAAA,QACF,KAAA,EAAA,IAAA,CAAA,KAAA;AACF,QAAC,SAAA,EAAA,IAAA,CAAA,SAAA;;;;AAxbC,QAAA,YAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,OAkCM,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CAAA,EAAA,GAAA,CAAA;AAAA,IAAA,IAjCA,CAAA,OAAA,IAAAL,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,MACH,GAAK,EAAA,CAAA;AAAA,MAAA,yBAAc,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA;AAAA,KAAA,EAAA;AAAoD,MAAAK,cAAY,CAAA,IAAsB,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAAA,MAAA;AAAmB,QAAS,IAAG,EAAA,CAAA;AAAmC,QAAwB,OAAA;AAA4C,UAAAC,sBAAA,CAAA,MAAA,EAAA;YAO3O,KAAA,EAAAL,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;AAAA,WAAA,EAAAM,mBAAA,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;AAEL,OAAA,CAAA;AAAA,KAUE,EAAA,CAAA,CAAA,IAAAC,sBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,IAAAC,kBAAA,CAAAH,sBAAA,CAAA,KAAA,EAAA;AAAA,MATgB,GAAA,EAAA,gBAAA;oCAShB,CAAA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA;AAAA,KARC,EAAA,IAAA,EAAA,CAAA,CAAA,EAAK;AAAgB,MAAA,CAAAI,SACf,EAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,CAAA;AAAA,KAAA,CAAA;AACC,GAAA,EAAA,CAAA,CAAA,CAAA;AACI,CAAA;AAEI,WACC,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}