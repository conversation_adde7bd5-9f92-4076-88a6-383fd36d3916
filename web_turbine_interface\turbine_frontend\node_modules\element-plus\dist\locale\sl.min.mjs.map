{"version": 3, "file": "sl.min.mjs", "sources": ["../../../../packages/locale/lang/sl.ts"], "sourcesContent": ["export default {\n  name: 'sl',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'V redu',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON>da<PERSON>',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON>tr<PERSON>',\n      selectDate: 'Izberi datum',\n      selectTime: 'Izberi čas',\n      startDate: 'Začetni datum',\n      startTime: 'Začetni čas',\n      endDate: 'Končni datum',\n      endTime: 'Končni čas',\n      prevYear: 'Prejšnje leto',\n      nextYear: 'Naslednje leto',\n      prevMonth: 'Prejšnji mesec',\n      nextMonth: 'Naslednji mesec',\n      year: '',\n      month1: 'Jan',\n      month2: 'Feb',\n      month3: 'Mar',\n      month4: 'Apr',\n      month5: 'Maj',\n      month6: 'Jun',\n      month7: 'Jul',\n      month8: 'Avg',\n      month9: 'Sep',\n      month10: 'Okt',\n      month11: 'Nov',\n      month12: 'Dec',\n      week: 'teden',\n      weeks: {\n        sun: 'Ned',\n        mon: 'Pon',\n        tue: 'Tor',\n        wed: 'Sre',\n        thu: 'Čet',\n        fri: 'Pet',\n        sat: 'Sob',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Avg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Nalaganje',\n      noMatch: 'Ni ustreznih podatkov',\n      noData: 'Ni podatkov',\n      placeholder: 'Izberi',\n    },\n    mention: {\n      loading: 'Nalaganje',\n    },\n    cascader: {\n      noMatch: 'Ni ustreznih podatkov',\n      loading: 'Nalaganje',\n      placeholder: 'Izberi',\n      noData: 'Ni podatkov',\n    },\n    pagination: {\n      goto: 'Pojdi na',\n      pagesize: '/stran',\n      total: 'Skupno {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Sporočilo',\n      confirm: 'V redu',\n      cancel: 'Prekliči',\n      error: 'Nedovoljen vnos',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Izbriši',\n      preview: 'Predogled',\n      continue: 'Nadaljuj',\n    },\n    table: {\n      emptyText: 'Ni podatkov',\n      confirmFilter: 'Potrdi',\n      resetFilter: 'Ponastavi',\n      clearFilter: 'Vse',\n      sumText: 'Skupno',\n    },\n    tree: {\n      emptyText: 'Ni podatkov',\n    },\n    transfer: {\n      noMatch: 'Ni ustreznih podatkov',\n      noData: 'Ni podatkov',\n      titles: ['Seznam 1', 'Seznam 2'],\n      filterPlaceholder: 'Vnesi ključno besedo',\n      noCheckedFormat: '{total} elementov',\n      hasCheckedFormat: '{checked}/{total} izbranih',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,qBAAqB,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,2BAA2B,CAAC,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}